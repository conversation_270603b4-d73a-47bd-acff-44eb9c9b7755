<template>
  <div class="create-page">
    <div class="main-content">
      <!-- <h1 class="page-title">AI智能创作工作室</h1> -->

      <!-- <Header class="chat-header" :transparent-strong="true" /> -->

      <!-- 添加动态背景效果 -->
      <div class="background-animation" v-if="false">
        <div class="light-effect light-effect-1"></div>
        <div class="light-effect light-effect-2"></div>
        <div class="light-effect light-effect-3"></div>
      </div>

      <!-- 添加粒子动画容器  -->
      <div class="particles-container" v-if="!creationStarted">
        <div class="particle" v-for="n in 40" :key="`particle-${n}`"></div>
      </div>

      <!-- 添加聊天面板 如果正在创作、没有加载历史记录、正在流式响应，则显示聊天面板 -->
       <!--  -->
      <div class="chat-centent" v-if="!creationStarted && !loadingHistory && isStreamingResponse">

        <!-- isStreamingResponse -->
        <div class="chat-loading" v-if="isStreamingResponse">
          <div class="chat-loading-container">
            <div class="loading-pulse"></div>
            <div class="loading-spinner"></div>
            <!-- 添加进度条和百分比显示 -->
            <div class="progress-container">
              <!-- <div class="progress-bar">
                <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
              </div> -->
              <div class="progress-text">{{ loadingProgress }}%</div>
            </div>
            <div class="loading-text">AI故事工坊正在为您创作...</div>


            <div class="loader-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            <div class="loading-decorations">
              <div class="decoration-item item-1"></div>
              <div class="decoration-item item-2"></div>
              <div class="decoration-item item-3"></div>
            </div>
          </div>
        </div>

        <!-- <ChatPanel v-else-if="!creationStarted" :messages="chatMessages" :isStreaming="isStreamingResponse" :streamingMessageId="streamingMessageId"
        :loading="loadingHistory" :summary="{
          styleName: selectedStyle ? styles.find(s => s.id === selectedStyle).name : '默认风格',
          actorNames: selectedActors.map(id => actors.find(a => a.id === id).name),
          creativeText: creativeText
        }" @send-message="sendMessage" @retry-message="retryMessage" @cancel-message="cancelMessage"
        @clear-context="handleClearContext" class="chat-centent-panel"/> -->

      </div>

      <!-- 创作进行中的布局 如果正在创作，则显示创作流程 -->
      <div class="creation-workspace" v-else="creationStarted" :class="{ 'is-resizing': isResizing }">
        <!-- 左侧聊天区域 -->
        <div class="chat-section-container" :class="{ 'collapsed': rightPanelCollapsed }"
          :style="{ flex: leftPanelFlex }">

          <!-- <Header class="chat-header" /> -->

          <div class="page-status-bar">
            <!-- 添加 tab 切换按钮 -->
            <div class="status-tabs">
              <!-- 创作状态 -->
              <!-- <div class="text-content">创作状态</div> -->
              <div v-for="tab in statusTabs" :key="tab.name" class="status-tab-item" :class="{
                'active': activeTab === tab.name,
                'completed': getTabResult(tab.name) && getTabResult(tab.name).status === 'completed'
              }" @click="handleTabClick(tab.name)">
                <div class="tab-header">
                  <!-- 使用 Element Plus 图标组件 -->
                  <div class="tab-icon">
                    <Pointer v-if="tab.name === 'session'" />
                    <SetUp v-if="tab.name === 'design'" />
                    <Document v-else-if="tab.name === 'script'" />
                    <User v-else-if="tab.name === 'edit'" />
                    <VideoCamera v-else-if="tab.name === 'animation'" />
                    <Collection v-else-if="tab.name === 'records'" />
                  </div>
                  <div class="tab-content">
                    <span class="tab-title">{{ tab.title }}</span>

                    <!-- 集成详细信息 -->
                    <div class="tab-summary"
                      v-if="getTabResult(tab.name) && getTabResult(tab.name).status !== 'in-progress' && getTabResult(tab.name).status === 'completed'">
                      <!-- 设计模块摘要 -->
                      <span v-if="tab.name === 'design' && designStory" class="summary-text">
                        {{ designStory.storyBasics?.title || '未命名故事' }} · {{ designStory.storyBasics?.genre || '未设置' }}
                      </span>

                      <!-- 故事模块摘要 -->
                      <span v-else-if="tab.name === 'script' && shotStory" class="summary-text">
                        {{ shotStory.chapters?.length || 0 }}个章节
                      </span>

                      <!-- 角色模块摘要 -->
                      <span v-else-if="tab.name === 'edit' && shotRoles" class="summary-text">
                        <span v-if="shotRoles.characters && shotRoles.characters.length > 0">主角：{{
                          shotRoles.characters[0]?.name || '未命名' }} · </span>{{ shotRoles.characters?.length || 0 }}个角色
                      </span>

                      <!-- 分镜模块摘要 -->
                      <span v-else-if="tab.name === 'animation' && shotShots" class="summary-text">
                        {{ shotShots.shotGroups?.length || 0 }}组分镜 · {{ getTotalShots() }}个镜头
                      </span>

                      <!-- 作品模块摘要 -->
                      <span v-else-if="tab.name === 'records'" class="summary-text">
                        {{ getRecordsCount() }}个作品
                      </span>
                    </div>

                    <div class="tab-next-button"
                      v-if="!isStreamingResponse && (!getTabResult(tab.name) || getTabResult(tab.name).status !== 'in-progress') && (tab.name === 'script' || tab.name === 'animation' || tab.name === 'edit')">
                      <span
                        v-if="tab.name === 'script' && getTabResult('design') && getTabResult('design').status === 'completed' && (!getTabResult('script') || getTabResult('script').status !== 'completed')"
                        @click.stop="insertTextSendMessage('调用工具生成故事')"> 生成故事 </span>
                      <span
                        v-if="tab.name === 'edit' && getTabResult('design') && getTabResult('design').status === 'completed' && (!getTabResult('edit') || getTabResult('edit').status !== 'completed')"
                        @click.stop="insertTextSendMessage('调用工具生成角色')"> 生成角色 </span>
                      <span
                        v-if="tab.name === 'animation' && getTabResult('script') && getTabResult('script').status === 'completed' && (!getTabResult('animation') || getTabResult('animation').status !== 'completed')"
                        @click.stop="insertTextSendMessage('调用工具生成分镜')"> 生成分镜 </span>
                    </div>

                  </div>
                  <!-- 使用 Element Plus 图标组件 -->
                  <!-- <div v-if="getTabResult(tab.name)" class="tab-status">
                    <Check v-if="getTabResult(tab.name).status === 'completed'" class="status-icon completed" />
                    <Loading v-else-if="getTabResult(tab.name).status === 'in-progress'"
                      class="status-icon in-progress" />
                  </div> -->
                </div>
              </div>
            </div>
          </div>

          <!-- :class="{ 'disabled': leftPanelCollapsed && !rightPanelCollapsed }" -->
          <button class="panel-toggle right-toggle" @click="toggleRightPanel"
            :title="rightPanelCollapsed ? '展开对话面板' : (leftPanelCollapsed ? '无法收起（另一面板已收起）' : '收起对话面板')">
            <el-icon>
              <ArrowLeft v-if="!rightPanelCollapsed" />
              <ArrowRight v-else />
            </el-icon>
          </button>
          <div class="chat-section">
            <ChatPanel :messages="chatMessages" :isStreaming="isStreamingResponse"
              :streamingMessageId="streamingMessageId" :loading="loadingHistory" :summary="{
                styleName: selectedStyle ? styles.find(s => s.id === selectedStyle).name : '默认风格',
                actorNames: selectedActors.map(id => actors.find(a => a.id === id).name),
                creativeText: creativeText
              }" @send-message="sendMessage" @retry-message="retryMessage" @cancel-message="cancelMessage"
              @clear-context="handleClearContext" />
          </div>
        </div>

        <!-- 中间调整条 -->
        <div class="panel-resizer" @mousedown="startResize"
          :class="{ 'collapsed': leftPanelCollapsed || rightPanelCollapsed }">
          <div class="resizer-handle"></div>
        </div>

        <!-- 右侧创作流程 -->
        <div class="creation-process-container" :class="{ 'collapsed': leftPanelCollapsed }"
          :style="{ flex: rightPanelFlex }">

          <CreationProcess v-model:activeTab="activeTab" :script-result="scriptResult" :voice-result="voiceResult"
            :edit-result="editResult" :animation-result="animationResult" :video-completed="videoCompleted"
            :creative-text="creativeText"
            :style-name="selectedStyle ? styles.find(s => s.id === selectedStyle).name : '默认风格'"
            v-model:videoProgress="videoProgress" v-model:volumeLevel="volumeLevel" :showShareOptions="showShareOptions"
            @toggle-share-options="toggleShareOptions" @download-video="downloadVideo"
            @fetch-ai-conversations-content-list="fetchAiConversationsContentList"
            @generation-records="handleGenerationRecords" :shotStory="shotStory" :shotScenes="shotScenes"
            :shotRoles="shotRoles" :shotShots="shotShots" :storyboards="storyboards" :design-result="designResult"
            :story-design="designStory" :conversation-id="conversationId" @update:storyDesign="handleDesignStoryUpdate"
            @update:shotStory="handleShotStoryUpdate" @update:shotShots="handleShotShotsUpdate"
            @update-records-count="updateRecordsCount" @update-shot-shots-count="updateShotShotsCount" :chat-panel-visible="!rightPanelCollapsed"
            :output-ratio="outputRatio" :voices="voices" :isLoadingVoices="isLoadingVoices"
            @refresh-voices="fetchVoices" @update:shotRoles="handleShotRolesUpdate" :session-result="chatSessionResult" />

          <!-- 隐藏按钮 -->
          <!-- :class="{ 'disabled': rightPanelCollapsed && !leftPanelCollapsed }" -->
          <button class="panel-toggle left-toggle" @click="toggleLeftPanel"
            :title="leftPanelCollapsed ? '展开创作面板' : (rightPanelCollapsed ? '无法收起（另一面板已收起）' : '收起创作面板')">
            <el-icon>
              <ArrowRight v-if="!leftPanelCollapsed" />
              <ArrowLeft v-else />
            </el-icon>
          </button>

          <div class="continue-creation" v-if="rightPanelCollapsed" @click="toggleRightPanel">
            <div class="continue-creation-title">
              <span>←继续创作</span>
            </div>
          </div>

        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch, inject, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import ChatPanel from '@/components/create/ChatPanel.vue'
import CreationProcess from '@/components/create/CreationProcess.vue'
import { ElMessage } from 'element-plus'
import { format, parseJSON } from 'date-fns'
import { EditPen, ArrowDown, ArrowLeft, ArrowRight, SetUp, Document, User, VideoCamera, Collection, Check, Loading, Pointer } from '@element-plus/icons-vue'
import chatService from '@/api/chat.js'
import userSession from '@/utils/userSession.js'
import { getConversationMessages, getAiConversationsContentList, stopConversation, clearContext, updateAiCreationContent, getVoiceList } from '@/api/auth.js'

// 使用 route 获取 URL 参数
const route = useRoute()
const router = useRouter()
const showEditOptions = ref(false)

// 用户积分
// 从App.vue注入积分相关的变量和函数
const userPoints = inject('userPoints')
const updateUserPoints = inject('updateUserPoints')

// 画面风格数据
const styles = [
  { id: 1, name: '吉卜力风格', category: '动画', preview: 'https://cdn1.genspark.ai/user-upload-image/5_generated/b9e916cc-ea76-4a3e-b610-246318630a9b_wm' },
  { id: 2, name: '儿童插画风', category: '插画', preview: 'https://cdn1.genspark.ai/user-upload-image/5_generated/b9e916cc-ea76-4a3e-b610-246318630a9b_wm' },
  { id: 3, name: '迪士尼风格', category: '动画', preview: 'https://cdn1.genspark.ai/user-upload-image/5_generated/b9e916cc-ea76-4a3e-b610-246318630a9b_wm' },
  { id: 4, name: '3D渲染风格', category: '3D', preview: 'https://cdn1.genspark.ai/user-upload-image/5_generated/b9e916cc-ea76-4a3e-b610-246318630a9b_wm' },
  { id: 5, name: '真实风格', category: '写实', preview: 'https://cdn1.genspark.ai/user-upload-image/5_generated/b9e916cc-ea76-4a3e-b610-246318630a9b_wm' },
  { id: 6, name: '日本动漫风格', category: '动画', preview: 'https://cdn1.genspark.ai/user-upload-image/5_generated/b9e916cc-ea76-4a3e-b610-246318630a9b_wm' }
]

// 角色数据
const actors = [
  { id: 1, name: '小明', shortName: '小', role: '主角', avatar: 'https://pkjutlj1mzid6tjw.public.blob.vercel-storage.com/virtualAnchor/avatar/f6422ffab41042ca9d62e724aad4a6ab-jFIcouulODrF53JLOx1k8QXVLqzM0A.png' },
  { id: 2, name: '小红', shortName: '小', role: '女主角', avatar: 'https://pkjutlj1mzid6tjw.public.blob.vercel-storage.com/virtualAnchor/avatar/f6422ffab41042ca9d62e724aad4a6ab-jFIcouulODrF53JLOx1k8QXVLqzM0A.png' },
  { id: 3, name: '机器人阿尔法', shortName: '机', role: '伙伴', avatar: 'https://pkjutlj1mzid6tjw.public.blob.vercel-storage.com/virtualAnchor/avatar/f6422ffab41042ca9d62e724aad4a6ab-jFIcouulODrF53JLOx1k8QXVLqzM0A.png' },
  { id: 4, name: '猫咪咪咪', shortName: '猫', role: '宠物', avatar: 'https://pkjutlj1mzid6tjw.public.blob.vercel-storage.com/virtualAnchor/avatar/f6422ffab41042ca9d62e724aad4a6ab-jFIcouulODrF53JLOx1k8QXVLqzM0A.png' },
  { id: 5, name: '超人队长', shortName: '超', role: '英雄', avatar: 'https://pkjutlj1mzid6tjw.public.blob.vercel-storage.com/virtualAnchor/avatar/f6422ffab41042ca9d62e724aad4a6ab-jFIcouulODrF53JLOx1k8QXVLqzM0A.png' },
  { id: 6, name: '小狗汪汪', shortName: '小', role: '宠物', avatar: 'https://pkjutlj1mzid6tjw.public.blob.vercel-storage.com/virtualAnchor/avatar/f6422ffab41042ca9d62e724aad4a6ab-jFIcouulODrF53JLOx1k8QXVLqzM0A.png' },
]

// 表单状态
const selectedStyle = ref(null)
const selectedActors = ref([])
const creativeText = ref('')
const confirmEachStep = ref(true)
const creationStarted = ref(false)
const highQuality = ref(false)

// 有声故事数量
const recordsCount = ref(0)
// 分镜章节数量
const shotShotsCount = ref(0)

// 视频播放相关状态
const videoCompleted = ref(false)
const videoProgress = ref(0)
const volumeLevel = ref(80)
const showShareOptions = ref(false)

// 标签页状态
const activeTab = ref('animation')

// 聊天相关状态
const chatMessages = ref([
  // {
  //   id: 'welcome',
  //   type: 'assistant',
  //   text: '你好！我是AI创作助手，请告诉我你想创作什么样的内容。你可以：\n1. 直接描述你的创意\n2. 选择画面风格和角色\n3. 让我来帮你优化创意'
  // },
  // {
  //   id: 'welcome',
  //   type: 'assistant',
  //   text: `### \u6b65\u9aa43\uff1a\u751f\u6210\u5206\u955c\u56fe\u50cf\n\u4ee5\u4e0b\u662f\u6bcf\u4e2a\u5206\u955c\u7684\u56fe\u50cf\u751f\u6210\u7ed3\u679c\uff0c\u98ce\u683c\u7edf\u4e00\u4e3a\u201c\u6697\u9ed1\u7ae5\u8bdd\u98ce\u683c\u201d\uff1a\n\n#### \u5206\u955c1\uff1a\u6708\u5149\u4e0b\u7684\u5e9f\u5f03\u5b85\u9662\n- **\u63d2\u753b\u63cf\u8ff0**\uff1a\u6708\u5149\u6d12\u5728\u9508\u8ff9\u6591\u6591\u7684\u94c1\u95e8\u4e0a\uff0c\u5ead\u9662\u4e2d\u592e\u6709\u4e00\u5ea7\u5e72\u6db8\u7684\u55b7\u6cc9\u3002\u5c0f\u5973\u5b69\u827e\u62c9\u62b1\u7740\u7834\u65e7\u7684\u5e03\u5076\u5154\u7ad9\u5728\u95e8\u5916\uff0c\u5e03\u5076\u5154\u7684\u73bb\u7483\u773c\u73e0\u53cd\u5c04\u7740\u6708\u5149\u3002\n- **\u56fe\u50cfURL**\uff1a[\u70b9\u51fb\u67e5\u770b](https://fal.media/files/penguin/gUkraTyji6B8peBtwM0bI_660eb3f14246416caca1960b5376142f.jpg)\n\n#### \u5206\u955c2\uff1a\u5b85\u9662\u4e8c\u697c\u4eae\u8d77\u70db\u5149\n- **\u63d2\u753b\u63cf\u8ff0**\uff1a\u5b85\u9662\u4e8c\u697c\u7684\u7a97\u53e3\u7a81\u7136\u4eae\u8d77\u70db\u5149\uff0c\u526a\u7eb8\u822c\u7684\u9ed1\u5f71\u63a0\u8fc7\u7a97\u7eb1\u3002\u94c1\u95e8\u65e0\u58f0\u5f00\u542f\uff0c\u9732\u51fa\u722c\u6ee1\u8346\u68d8\u7684\u5ead\u9662\u5c0f\u5f84\u3002\n- **\u56fe\u50cfURL**\uff1a[\u70b9\u51fb\u67e5\u770b](https://fal.media/files/monkey/vQPDcbee5fztHlv5S3yUE_1c5538e907ab4ec7891bbf9ab92de9ee.jpg)\n\n#### \u5206\u955c3\uff1a\u9910\u5385\u957f\u684c\u7684\u8be1\u5f02\u573a\u666f\n- **\u63d2\u753b\u63cf\u8ff0**\uff1a\u9910\u5385\u957f\u684c\u4e0a\u6446\u7740\u53d1\u9709\u7684\u8336\u70b9\uff0c\u4e03\u628a\u9ad8\u80cc\u6905\u4e0a\u6709\u6df1\u6d45\u4e0d\u4e00\u7684\u538b\u75d5\u3002\u7b2c\u516d\u628a\u6905\u5b50\u7a81\u7136\u88ab\u62c9\u5f00\uff0c\u94f6\u9910\u5200\u81ea\u884c\u5207\u5f00\u82b1\u74f6\u91cc\u7684\u67af\u73ab\u7470\u3002\n- **\u56fe\u50cfURL**\uff1a[\u70b9\u51fb\u67e5\u770b](https://fal.media/files/monkey/EPCIMK2wLeFGZRZZ6ojUr_6138f3fcfe1e4064918c16d924f3f53e.jpg)\n\n#### \u5206\u955c4\uff1a\u9601\u697c\u955c\u5b50\u7684\u7ae5\u8c23\n- **\u63d2\u753b\u63cf\u8ff0**\uff1a\u9601\u697c\u7684\u955c\u5b50\u6d6e\u73b0\u5b69\u7ae5\u624b\u5370\uff0c\u5e03\u5076\u5154\u7684\u68c9\u7d6e\u4ece\u88c2\u7f1d\u4e2d\u98d8\u51fa\uff0c\u7ec4\u6210\u6a21\u7cca\u7684\u7ae5\u8c23\u5b57\u53e5\u3002\n- **\u56fe\u50cfURL**\uff1a[\u70b9\u51fb\u67e5\u770b](https://fal.media/files/panda/vqBhIUpsdc-gunbKP1oQc_d72e26638d4a41d2b7443ece11a0b8ae.jpg)\n\n#### \u5206\u955c5\uff1a\u9ece\u660e\u65f6\u5206\u767d\u73ab\u7470\n- **\u63d2\u753b\u63cf\u8ff0**\uff1a\u9ece\u660e\u65f6\u5206\uff0c\u5927\u95e8\u91cd\u65b0\u51fa\u73b0\u3002\u827e\u62c9\u62b1\u7740\u6062\u590d\u5d2d\u65b0\u7684\u5e03\u5076\u5154\u7ad9\u5728\u95e8\u53e3\uff0c\u8eab\u540e\u5b85\u9662\u8fc5\u901f\u722c\u6ee1\u76db\u5f00\u7684\u767d\u73ab\u7470\u3002\n- **\u56fe\u50cfURL**\uff1a[\u70b9\u51fb\u67e5\u770b](https://fal.media/files/rabbit/zfkAgLqPuTDwhSxX98CRq_e2270796810144a0aa02ddad05fdea99.jpg)\n\n\u60a8\u5bf9\u8fd9\u4e9b\u56fe\u50cf\u6ee1\u610f\u5417\uff1f\u5982\u679c\u9700\u8981\u8c03\u6574\uff0c\u8bf7\u544a\u8bc9\u6211\uff01\u5982\u679c\u6ee1\u610f\uff0c\u6211\u5c06\u7ee7\u7eed\u751f\u6210\u8bed\u97f3\u65c1\u767d\uff08\u53ef\u9009\uff09\u3002`
  // }
])



// 流式响应状态
const isStreamingResponse = ref(false)
const streamingMessageId = ref(null)
// 会话ID：从userSession中获取，如果存在则使用现有会话，否则为新会话
const conversationId = ref(null)
// 动态获取用户ID，确保每次都获取最新的
const userId = computed(() => userSession.getUserId())
// 是否正在加载历史消息
const loadingHistory = ref(false)

// 加载进度相关状态
const loadingProgress = ref(0)
let progressTimer = null

// 创作结果状态
const chatSessionResult = ref({ status: 'completed' }) // 项目创意
const scriptResult = ref(null) // 故事生成结果
const voiceResult = ref(null) // 场景生成结果
const editResult = ref(null) // 角色生成结果
const animationResult = ref(null) // 分镜生成结果
const designResult = ref(null) // 故事设计结果

// 添加语音数据
const voices = ref([])
const isLoadingVoices = ref(false)

// {
//   "storyBasics": {
//     "title": "吸血鬼的素食餐厅",
//     "genre": "奇幻/幽默",
//     "targetAudience": "6-9岁",
//     "length": {
//       "chapters": 5
//     }
//   },
//   "themeAndTone": {
//     "mainTheme": "打破刻板印象，接纳不同",
//     "writingStyle": "轻松幽默，充满想象力"
//   },
//   "characters": {
//     "protagonist": {
//       "name": "弗拉德",
//       "description": "一位300岁的吸血鬼，厌倦了吸血生活，决定开一家素食餐厅。他穿着黑色燕尾服，皮肤苍白但性格温和，梦想是证明吸血鬼也可以善良。"
//     },
//     "antagonist": {
//       "name": "狼人卢克",
//       "description": "固执的传统主义者，认为吸血鬼就该吸血。他肌肉发达，毛发浓密，经常来餐厅捣乱，试图证明素食不适合超自然生物。"
//     },
//     "supportingCharacters": [
//       "小女巫莉莉（餐厅甜点师）",
//       "僵尸服务员鲍勃"
//     ]
//   },
//   "plotOutline": {
//     "setup": "在人类和超自然生物共存的小镇上，吸血鬼弗拉德开了一家名为'月光胡萝卜'的素食餐厅，引起轰动。",
//     "conflict": "狼人卢克不断质疑弗拉德的选择，认为他背叛了吸血鬼的传统。",
//     "climax": "卢克在餐厅举办'超自然美食大赛'，试图让弗拉德出丑。",
//     "resolution": "弗拉德的创意素食料理征服了所有评委，卢克也爱上了素肉丸，两人和解。"
//   },
//   "keyElements": {
//     "emotionalJourney": "弗拉德从自我怀疑到自信，最终用行动证明善良比传统更重要。",
//     "uniqueAspects": [
//       "吸血鬼做素食厨师的反差设定",
//       "超自然生物融入人类社会的世界观",
//       "用幽默方式探讨包容主题"
//     ]
//   },
//   "metadata": {
//     "version": "1.0",
//     "status": "draft",
//     "lastUpdated": "2023-11-15"
//   }
// }
const designStory = ref(null) // 设计故事

// {
//   "chapters": [
//     {
//       "chapterTitle": "故事名称（根据章节内容取一个吸引人的标题）",
//       "chapterNumber": "章节编号",
//       "totalShots": "分镜的总数量（建议每个场景2-4个镜头）",
//       "scenes": [
//         {
//           "id": "SC-01",
//           "disc": "第一个场景的视觉和环境详细描述，突出场景特色",
//           "shots": [
//             {
//               "shot": 1,
//               "summary": "第一个镜头的简要视觉描述和事件概述",
//               "line": "第一个镜头中的角色对白或旁白"
//             }
//           ]
//         }
//       ]
//     }
//   ]
// }
const shotStory = ref(null) // 生成的故事

// {
//   "scenes": [
//     {
//       "ID": "43",
//       "name": "场景A",
//       "description": "这是场景A的详细描述文本。场景是故事发生的环境背景，为角色提供互动的空间，同时也塑造了故事的氛围和基调。",
//       "image": "https://cdn1.genspark.ai/user-upload-image/5_generated/d0456e42-dd3c-4cec-8019-d26e59f7a9e4_wm"
//     }
//   ]
// }
const shotScenes = ref(null) // 生成的场景

// {
//   "character":[
//     {
//       "charID": "43",
//       "name": "角色A",
//       "description": "角色A描述",
//       "image": "https://pkjutlj1mzid6tjw.public.blob.vercel-storage.com/virtualAnchor/avatar/f6422ffab41042ca9d62e724aad4a6ab-jFIcouulODrF53JLOx1k8QXVLqzM0A.png"
//     }
//   ]
// }
const shotRoles = ref(null) // 生成的角色

// {
//   "shotGroups": [
//     {
//       "scene_id": "SC1",
//       "sceneName": "清晨厨房",
//       "segment_id": "Segment_1",
//       "totalShots": 2,
//       "shots": [
//         {
//           "id": "WIDE_SC1_S1",
//           "type": "全景",
//           "movement": "静止",
//           "composition": "居中构图",
//           "image":"https://cdn1.genspark.ai/user-upload-image/5_generated/b9e916cc-ea76-4a3e-b610-246318630a9b_wm",
//           "characters": [
//             "小明",
//             "小红"
//           ],
//           "lines": {
//             "小明": "(打招呼, 微笑) 早上好，今天的天气真不错！",
//             "小红": "(走进厨房, 兴奋) 我来帮你！"
//           }
//         }
//       ]
//     }
//   ]
// }
const shotShots = ref(null) // 生成的分镜

const storyboards = ref(null) // 生成的分镜预览


const insertTextSendMessage = (text) => {
  if (window.insertTextSendMessage && typeof window.insertTextSendMessage === 'function') {
    window.insertTextSendMessage(text);
  }
}

// 从演员列表中移除
const removeActor = (actorId) => {
  const index = selectedActors.value.indexOf(actorId)
  if (index > -1) {
    selectedActors.value.splice(index, 1)
  }
}

// 跳转到历史记录
const handleGenerationRecords = () => {
  router.push({
    path: '/generation-records',
    query: {
      conversationId: conversationId.value
    }
  })
}

// 取消消息
const cancelMessage = () => {
  console.log('取消消息')

  // 取消前端流式请求
  const cancelled = chatService.cancelStreamingRequest()

  // 如果有正在流式传输的消息，更新它的状态
  if (streamingMessageId.value && cancelled) {
    const messageIndex = chatMessages.value.findIndex(m => m.id === streamingMessageId.value)
    if (messageIndex !== -1) {
      // 更新消息状态为已取消
      chatMessages.value[messageIndex].status = {
        progress: 0,
        type: 'warning',
        text: '回复已取消'
      }

      // 添加取消标记到消息文本末尾
      if (!chatMessages.value[messageIndex].text.endsWith('（已取消）')) {
        chatMessages.value[messageIndex].text += '\n\n（已取消）'
      }
    }

    // 重置流式响应状态
    isStreamingResponse.value = false
    streamingMessageId.value = null

    // 停止进度模拟
    stopProgressSimulation()
  }

  // 同时调用后端 API 停止响应生成
  if (conversationId.value) {
    stopConversation(conversationId.value)
      .then(() => console.log('服务器响应已停止'))
      .catch(err => console.error('停止服务器响应失败:', err))
  }
}

// 发送消息
const sendMessage = async (message, isUser = true) => {
  // !message.trim() || 
  if (isStreamingResponse.value) return
  chatMessages.value.forEach(item => {
    item.suggestedQuestions = []
  })

  const messageId = Date.now().toString(36)

  if (message && isUser) {
    // 添加用户消息
    chatMessages.value.push({
      id: messageId,
      type: 'user',
      text: message
    })
  }

  // 添加AI回复的占位消息
  const aiMessageId = `ai-${Date.now().toString(36)}`
  streamingMessageId.value = aiMessageId

  chatMessages.value.push({
    id: aiMessageId,
    type: 'assistant',
    text: '',
    status: {
      progress: 0,
      type: 'warning',
      text: '正在思考中...'
    },
    suggestedQuestions: [] // 初始化建议问题数组
  })

  isStreamingResponse.value = true

  // 开始进度模拟
  startProgressSimulation()

  try {
    // 准备创作相关上下文
    const style = selectedStyle.value ? styles.find(s => s.id === selectedStyle.value).name : '默认风格'
    const characters = selectedActors.value.map(id => actors.find(a => a.id === id).name).join('、')

    // 构建发送给Dify的参数
    const inputs = {}
    if (style !== '默认风格') {
      inputs.style = style
    }
    if (characters) {
      inputs.characters = characters
    }

    // 发送请求并处理流式响应
    await chatService.sendStreamingChatMessage({
      inputs,
      query: message,
      conversation_id: conversationId.value,
      user: userId.value,
      // files: []
    },
      // 处理流式响应的回调
      (chunk) => {
        const currentMessageIndex = chatMessages.value.findIndex(m => m.id === aiMessageId)
        if (currentMessageIndex !== -1) {
          // 根据事件类型处理响应数据
          if ((chunk.event === 'agent_message' || chunk.event === 'message') && chunk.answer !== undefined) {
            // 更新消息内容，确保换行符被保留
            chatMessages.value[currentMessageIndex].text = chatMessages.value[currentMessageIndex].text + chunk.answer;
            // 思考过程可以单独处理
            // console.log('chunk.answer:', chatMessages.value[currentMessageIndex].text);

            // 更新进度信息
            if (chatMessages.value[currentMessageIndex].status) {
              chatMessages.value[currentMessageIndex].status.progress = 50;
              chatMessages.value[currentMessageIndex].status.text = '正在生成回复...';
            }
          } else if (chunk.event === 'agent_thought') {
            console.log('chunk.thought:', chunk);

            if (chunk.tool) {

              // 最终结果
              if (chunk.thought) {
                chatMessages.value[currentMessageIndex].text = chunk.thought;
              }

              // let observation = chunk.observation ? ('\n输出: \n' + chunk.observation + '\n') : ''
              // let tool =
              //   "\n##### 调用工具 " +
              //   chunk.tool + '\n' +
              //   "```\n" +
              //   "输入: \n" + chunk.tool_input + '\n' +
              //   observation +
              //   "```\n";
              // chatMessages.value[currentMessageIndex].text = chatMessages.value[currentMessageIndex].text + '\n' + tool;

              let inputTool =
                // "\n##### 调用工具 " +
                // chunk.tool + '\n' +
                "```\n" +
                "input: \n" + chunk.tool_input
              chatMessages.value[currentMessageIndex].text = chatMessages.value[currentMessageIndex].text + '\n' + inputTool;

              if (chunk.observation) {
                let outputTool =
                  "\n" +
                  "output: \n" + chunk.observation
                chatMessages.value[currentMessageIndex].text = chatMessages.value[currentMessageIndex].text + '\n' + outputTool;
              }
              chatMessages.value[currentMessageIndex].text = chatMessages.value[currentMessageIndex].text + '\n' +
                "```\n";

            }

          } else if (chunk.event === 'message_end') {
            creationStarted.value = true
            if (chunk.message_id) {
              console.log('获取消息ID:', chunk.message_id);
              console.log('消息结束事件完整数据:', chunk);

              // 处理usage信息
              if (chunk.metadata && chunk.metadata.usage) {
                console.log('获取usage信息:', JSON.stringify(chunk.metadata.usage));

                // 创建消息副本
                const updatedMessage = { ...chatMessages.value[currentMessageIndex] };

                // 将usage信息添加到消息对象
                updatedMessage.usage = chunk.metadata.usage;
                updatedMessage.id = updatedMessage.id + '_updated'; // 修改ID强制组件重新渲染

                // 更新消息数组
                chatMessages.value.splice(currentMessageIndex, 1, updatedMessage);

                let updateMessage = JSON.stringify(chatMessages.value[currentMessageIndex])
                // 打印更新后的消息对象
                console.log('更新后的消息对象:', updateMessage);

                // 强制更新视图
                chatMessages.value = [...chatMessages.value];

                // 添加延时再次尝试更新视图
                setTimeout(() => {
                  const updatedMessage = { ...chatMessages.value[currentMessageIndex] };
                  chatMessages.value.splice(currentMessageIndex, 1, updatedMessage);
                  chatMessages.value = [...chatMessages.value];
                  console.log('再次尝试更新视图 - usage数据:', JSON.stringify(updatedMessage.usage));
                }, 100);

                // 更新积分余额
                if (chunk.metadata.usage.remainingPoints) {
                  updateUserPoints(chunk.metadata.usage.remainingPoints)
                }
              } else {
                console.warn('metadata或usage信息缺失:', chunk.metadata);
              }
            }

            // 消息结束时更新状态
            if (chatMessages.value[currentMessageIndex].status) {
              chatMessages.value[currentMessageIndex].status.progress = 100;
              chatMessages.value[currentMessageIndex].status.text = '回复完成';
            }
          } else if (chunk.event === 'eventPage') {
            console.log('eventPage:', chunk.eventPage);

            window.toActiveTab(chunk.eventPage);

          } else if (chunk.answer !== undefined) {
            // 处理没有明确事件类型但有答案的情况
            // chatMessages.value[currentMessageIndex].text = chunk.answer;
            console.log('chunk.event - chunk.answer:', chunk.answer);

            // 更新进度信息
            if (chatMessages.value[currentMessageIndex].status) {
              chatMessages.value[currentMessageIndex].status.progress = 50;
              chatMessages.value[currentMessageIndex].status.text = '正在生成回复...';
            }
          }

          // // 保存会话ID，但只保存在组件的ref中，不存储到userSession
          // if (chunk.conversation_id && !conversationId.value) {
          //   conversationId.value = chunk.conversation_id;
          //   // 保存会话ID到userSession，方便跨页面使用
          //   userSession.saveConversationId(chunk.conversation_id);
          //   console.log('保存新的会话ID到本地存储:', chunk.conversation_id);
          // }
        }
      },
      // 完成回调
      (finalResponse) => {
        const currentMessageIndex = chatMessages.value.findIndex(m => m.id === aiMessageId)
        console.log('currentMessageIndex:', currentMessageIndex)
        if (currentMessageIndex !== -1) {
          console.log('chatMessages.value[currentMessageIndex]', chatMessages.value[currentMessageIndex])
          // 确保有文本内容
          if (!chatMessages.value[currentMessageIndex].text && finalResponse) {
            chatMessages.value[currentMessageIndex].text = finalResponse;
          }

          // 文本格式整理，确保换行符正确
          // if (chatMessages.value[currentMessageIndex].text) {
          // 移除多余的空行，但保留正常换行
          // const text = chatMessages.value[currentMessageIndex].text
          // .replace(/\n{3,}/g, '\n\n') // 替换3个以上连续换行为2个
          // .trim();
          //   chatMessages.value[currentMessageIndex].text = text;
          // }

          // 更新最终状态
          chatMessages.value[currentMessageIndex].status = {
            progress: 100,
            type: 'success',
            text: '回复完成'
          }

        }

        isStreamingResponse.value = false
        streamingMessageId.value = null

        // 停止进度模拟
        stopProgressSimulation()

        // 如果 updateMessage 不包含 [!WARNING] 则发送「继续生成」消息
        // if (!chatMessages.value[chatMessages.value.length - 1].text.includes('[!WARNING]')) {
        //   console.log('updateMessage 不包含 [!WARNING] 则发送「继续创作」消息')
        //   if (sendCount < 1) {
        //     sendCount++
        //     sendMessage('继续创作', false)
        //   } else {
        //     sendCount = 0
        //     chatMessages.value.push({
        //       id: 'welcome',
        //       type: 'assistant',
        //       text: '连续创作超1次，可以继续创作内容。',
        //       status: {
        //         progress: 100,
        //         type: 'success',
        //         text: '连续创作'
        //       }
        //     })
        //   }
        // } else {
        //   sendCount = 0
        //   console.log('updateMessage 包含 [!WARNING] 则不发送「继续创作」消息')
        // }

      },
      // 错误回调
      (error) => {
        console.error('Dify API error:', error);
        const currentMessageIndex = chatMessages.value.findIndex(m => m.id === aiMessageId)
        if (error.message === '请求已取消') {
          // chatMessages.value[currentMessageIndex].text = '请求已取消'
          chatMessages.value[currentMessageIndex].status = {
            progress: 0,
            type: 'error',
            text: '请求已取消'
          }
        } else {
          chatMessages.value[currentMessageIndex].text = '抱歉，发生了错误，请重试。' + error
          chatMessages.value[currentMessageIndex].status = {
            progress: 0,
            type: 'error',
            text: '发生错误'
          }
        }

        isStreamingResponse.value = false
        streamingMessageId.value = null

        // 停止进度模拟
        stopProgressSimulation()
      })
  } catch (error) {
    console.error('Error sending message to Dify:', error)
    const currentMessageIndex = chatMessages.value.findIndex(m => m.id === aiMessageId)
    if (currentMessageIndex !== -1) {
      chatMessages.value[currentMessageIndex].text = '抱歉，发生了错误，请重试。'
      chatMessages.value[currentMessageIndex].status = {
        progress: 0,
        type: 'error',
        text: '发生错误'
      }
    }

    isStreamingResponse.value = false
    streamingMessageId.value = null

    // 停止进度模拟
    stopProgressSimulation()
  }
}

var sendCount = 0

// 重试消息
const retryMessage = (messageId) => {
  const messageIndex = chatMessages.value.findIndex(m => m.id === messageId)
  if (messageIndex > 0) {
    // 找到对应的用户消息
    const userMessageIndex = messageIndex - 1
    if (userMessageIndex >= 0 && chatMessages.value[userMessageIndex].type === 'user') {
      const userMessage = chatMessages.value[userMessageIndex].text

      // 移除AI响应
      chatMessages.value.splice(messageIndex, 1)

      // 重新发送
      sendMessage(userMessage)
    }
  }
}

// 切换分享选项显示状态
const toggleShareOptions = () => {
  showShareOptions.value = !showShareOptions.value
}

// 下载视频功能
const downloadVideo = () => {
  // 模拟下载功能
  ElMessage({
    message: '视频正在下载中，请稍候...',
    type: 'info'
  })

  // 模拟下载延迟
  setTimeout(() => {
    ElMessage.success('视频下载完成！')
  }, 2000)
}

// 格式化时间
const formatTime = (date) => {
  return format(date, 'yyyy-MM-dd HH:mm')
}

// 播放视频
const playVideo = (video) => {
  if (video.status === 'completed') {
    // 实现播放逻辑
    ElMessage.success('开始播放视频')
  }
}

// 分享视频
const shareVideo = (video) => {
  if (video.status === 'completed') {
    // 使用路由导航到Share页面
    router.push({
      name: 'Share',
      params: { id: video.id }
    })
  }
}

// 加载故事数据（编辑模式）
const aiConversations = async () => {
  try {
    // 使用Promise.all并行处理所有请求，提高加载效率
    await Promise.all([
      // fetchAiConversationsContentList({ contentType: 1 }),
      // fetchAiConversationsContentList({ contentType: 2 }),
      fetchAiConversationsContentList({ contentType: 3 }),
      // fetchAiConversationsContentList({ contentType: 4 }),
      // fetchAiConversationsContentList({ contentType: 10 })
    ]);
    // await fetchAiConversationsContentList({contentType:5})
    return true;
  } catch (error) {
    console.error('加载故事数据失败:', error);
    return false;
  }
}

// 清空上下文
const handleClearContext = async () => {
  console.log('Create.vue: handleClearContext 被调用')
  if (conversationId.value) {
    try {
      console.log('Create.vue: 调用 clearContext API，conversationId =', conversationId.value)
      const response = await clearContext(conversationId.value)
      console.log('Create.vue: clearContext API 调用成功', response)
      ElMessage.success('上下文已清空')
      // 清空聊天记录，只保留欢迎消息
      chatMessages.value = [{
        id: 'welcome',
        type: 'assistant',
        text: '上下文已清空，现在可以继续创作内容。',
        status: {
          progress: 100,
          type: 'success',
          text: '上下文已清空'
        }
      }]
    } catch (error) {
      console.error('Create.vue: clearContext API 调用失败', error)
      ElMessage.error('清空上下文失败，请重试')
    }
  } else {
    console.warn('Create.vue: 没有有效的 conversationId，无法清空上下文')
    ElMessage.warning('没有有效的会话ID')
  }
}

// 加载会话历史消息
const loadHistoryMessages = async () => {
  // 如果没有会话ID，则不需要加载历史消息
  if (!conversationId.value) return;

  try {
    loadingHistory.value = true;

    // 调用API获取历史消息
    const response = await getConversationMessages(conversationId.value);

    // 如果有历史消息，将它们添加到聊天列表中
    if (response && response.success && response.data && response.data.data && response.data.data.length > 0) {
      // 清空当前聊天记录
      chatMessages.value = [];

      // 将历史消息转换为适合显示的格式并添加到聊天记录中
      response.data.data.forEach(message => {
        // 添加用户消息
        if (message.query) {
          chatMessages.value.push({
            id: message.id + '-query',
            type: 'user',
            text: message.query
          });
        }

        // 添加AI回复
        if (message.agent_thoughts) {
          var content = ""
          message.agent_thoughts.forEach(thought => {
            // 最终结果
            if (thought.thought) {
              content = content + thought.thought;
            }
            if (thought.tool) {
              let inputTool =
                // "\n##### 执行工具 " +
                // thought.tool + '\n' +
                "```\n" +
                "input: \n" + thought.tool_input
              content = content + '\n' + inputTool;

              if (thought.observation) {
                let outputTool =
                  "\n" +
                  "output: \n" + thought.observation
                content = content + '\n' + outputTool
              }
              content = content + '\n' +
                "```\n"
            }
          });

          chatMessages.value.push({
            id: message.id,
            type: 'assistant',
            text: content
          });
        }
      });

      console.log('已加载历史消息:', chatMessages.value.length);
    } else {
      console.log('没有历史消息');
      // 发送消息
      sendMessage("开始创作");
    }

    loadingHistory.value = false;
  } catch (error) {
    console.error('加载历史消息失败:', error);
    ElMessage.error('加载历史消息失败');
    loadingHistory.value = false;
  }
};

// 监听暗色模式变化
let darkModeObserver = null;

// 获取Ai创作内容
const fetchAiConversationsContentList = async (data) => {
  // 解析参数，支持数字或对象格式
  let contentType = data.contentType
  try {
    // 如果没有conversationId，则不进行请求
    if (!conversationId.value) {
      console.log('无法获取AI创作内容：会话ID不存在')
      return
    }

    console.log('fetchAiConversationsContentList called with:', data.contentType, '会话ID:', conversationId.value)

    // 避免在组件实例不存在时更新响应式状态
    // 使用本地变量而不是直接更新响应式状态
    let currentDesignResult = null
    let currentScriptResult = null
    let currentVoiceResult = null
    let currentEditResult = null
    let currentAnimationResult = null

    if (contentType == 10 || contentType == 5) {
      currentDesignResult = { status: 'in-progress' }
      // 只有在组件实例存在时才更新响应式状态
      if (designResult) designResult.value = currentDesignResult
    } else if (contentType == 1) {
      currentScriptResult = { status: 'in-progress' }
      if (scriptResult) scriptResult.value = currentScriptResult
    } else if (contentType == 2) {
      currentVoiceResult = { status: 'in-progress' }
      if (voiceResult) voiceResult.value = currentVoiceResult
    } else if (contentType == 3) {
      currentEditResult = { status: 'in-progress' }
      if (editResult) editResult.value = currentEditResult
    } 
    
    // else if (contentType == 4) {
    //   currentAnimationResult = { status: 'in-progress' }
    //   if (animationResult) animationResult.value = currentAnimationResult
    // }

    const response = await getAiConversationsContentList(contentType, conversationId.value)
    if (response.data) {
      console.log('获取AI创作内容成功:', contentType)

      if (contentType == 10 || contentType == 5) { // 故事设计
        // 只有在组件实例存在时才更新响应式状态
        if (designStory) {
          designStory.value = response.data;
          console.log('更新故事设计数据成功')
          if (designStory.value && designResult) {
            designResult.value = { status: 'completed' }
            // outputRatio.value = designStory.value.themeAndTone.size
          }
        }
      } else if (contentType == 1) { // 生成的故事
        if (shotStory) {
          shotStory.value = response.data;
          console.log('更新故事数据成功')
          if (shotStory.value && shotStory.value.chapters && shotStory.value.chapters.length > 0 && scriptResult) {
            scriptResult.value = { status: 'completed' }
          }
        }
      } else if (contentType == 2) { // 生成的场景
        if (shotScenes) {
          shotScenes.value = response.data;
          console.log('更新场景数据成功', shotScenes.value)
          if (shotScenes.value && shotScenes.value.scenes && shotScenes.value.scenes.length > 0 && voiceResult) {
            voiceResult.value = { status: 'completed' }
          }
        }
      } else if (contentType == 3) { // 生成的角色
        if (shotRoles) {
          console.log('准备更新角色数据:', JSON.stringify(response.data).substring(0, 200) + '...')
          // 使用解构方式创建新对象，确保触发响应式更新
          shotRoles.value = { ...response.data };
          console.log('更新角色数据成功，当前数据:',
            '角色数量:', shotRoles.value?.characters?.length,
            '数据预览:', JSON.stringify(shotRoles.value).substring(0, 200) + '...')
          if (shotRoles.value && shotRoles.value.characters && shotRoles.value.characters.length > 0 && editResult) {
            editResult.value = { status: 'completed' }
            creationStarted.value = true
          }
        }
      } 
      // else if (contentType == 4) { // 生成的分镜
      //   if (shotShots) {
      //     console.log('准备更新分镜数据:', JSON.stringify(response.data).substring(0, 200) + '...')
      //     // 使用解构方式创建新对象，确保触发响应式更新
      //     shotShots.value = { ...response.data };
      //     console.log('更新分镜数据成功，当前数据:',
      //       '分镜组数量:', shotShots.value?.shotGroups?.length,
      //       '数据预览:', JSON.stringify(shotShots.value).substring(0, 200) + '...')
      //     if (shotShots.value && shotShots.value.shotGroups && shotShots.value.shotGroups.length > 0 && animationResult) {
      //       animationResult.value = { status: 'completed' }
      //     }
      //   }
      // }

      // else if (contentType == 5) { // 分镜预览
      //   console.log('准备更新分镜预览数据:', JSON.stringify(response.data).substring(0, 200) + '...')
      //   // 使用解构方式创建新对象，确保触发响应式更新
      //   storyboards.value = { ...response.data };
      //   console.log('更新分镜预览数据成功')
      // }
    } else {
      console.error('获取AI创作' + contentType + '失败:', response.data?.errMessage || '未知错误')
    }
  } catch (error) {
    console.error('获取AI创作内容异常:', error)
    // 只在组件实例存在时显示消息
    if (typeof ElMessage !== 'undefined') {
      ElMessage.error('获取创作内容失败，请稍后重试')
    }
  } finally {
    // 确保只在组件实例存在时更新响应式状态
    if (contentType == 10 || contentType == 5) {
      if (designResult && (!designResult.value || designResult.value.status != 'completed')) {
        designResult.value = null
      }
    } else if (contentType == 1) {
      if (scriptResult && (!scriptResult.value || scriptResult.value.status != 'completed')) {
        scriptResult.value = null
      }
    } else if (contentType == 2) {
      if (voiceResult && (!voiceResult.value || voiceResult.value.status != 'completed')) {
        voiceResult.value = null
      }
    } else if (contentType == 3) {
      if (editResult && (!editResult.value || editResult.value.status != 'completed')) {
        editResult.value = null
      }
    } 
    
    // else if (contentType == 4) {
    //   if (animationResult && (!animationResult.value || animationResult.value.status != 'completed')) {
    //     animationResult.value = null
    //   }
    // }
  }
}

onUnmounted(() => {
  window.toActiveTab = null;
  // 清理暗色模式观察器
  if (darkModeObserver) {
    darkModeObserver.disconnect();
  }
  // 清理进度定时器
  if (progressTimer) {
    clearInterval(progressTimer);
  }
})

// 组件加载时初始化
onMounted(async () => {
  // 初始化粒子动画
  nextTick(() => {
    animateParticles();
  });

  // 添加暗色模式切换监听
  darkModeObserver = new MutationObserver(() => {
    // 检测到body类名变化时，更新粒子颜色
    const isDarkMode = document.body.classList.contains('dark');
    document.querySelectorAll('.particle').forEach(particle => {
      const lightColor = particle.getAttribute('data-light-color');
      const darkColor = particle.getAttribute('data-dark-color');
      particle.style.backgroundColor = isDarkMode ? darkColor : lightColor;
    });
  });

  // 监听body类名变化
  darkModeObserver.observe(document.body, { attributes: true, attributeFilter: ['class'] });

  // 处理如果有conversationId参数，则获取历史消息
  console.log('Create.vue: onMounted 被调用', route.query)

  // 跳转到指定标签页
  window.toActiveTab = (tab) => {
    if (tab == '10') {
      activeTab.value = 'design' // 故事设计
    }
    // else if (tab == '1') {
    //   activeTab.value = 'script' // 故事生成
    // } 
    else if (tab == '3') {
      activeTab.value = 'edit' // 角色生成
    } else if (tab == '4') {
      activeTab.value = 'animation' // 分镜生成
    } 
    // else if (tab == '5') {
    //   activeTab.value = 'records' // 生成有声故事
    // }

    // 确保在组件已挂载的状态下调用
    nextTick(() => {
      fetchAiConversationsContentList({ contentType: tab })
    })
  }

  if (route.query.conversationId) {
    conversationId.value = route.query.conversationId
    // 获取风格和比例参数
    // if (route.query.style) {
    //   selectedStyle.value = parseInt(route.query.style)
    // }
    // if (route.query.ratio) {
    //   outputRatio.value = route.query.ratio
    // }

    try {
      // 使用nextTick确保组件已渲染
      await nextTick();
      await loadHistoryMessages(conversationId.value);

      // 加载所有创作数据
      console.log('开始加载所有创作数据...')
      await aiConversations();

      // 添加短暂延迟，确保数据已正确加载并响应式更新
      setTimeout(() => {
        // 根据URL参数设置当前激活的标签
        if (route.query.tab) {
          console.log('根据URL参数切换到标签:', route.query.tab)
          activeTab.value = route.query.tab;
        }

        console.log('数据加载完成，当前数据状态:',
          '角色数量:', shotRoles.value?.characters?.length,
          '场景数量:', shotScenes.value?.scenes?.length,
          '分镜组数量:', shotShots.value?.shotGroups?.length)
      }, 300)
    } catch (error) {
      console.error('初始化数据加载失败:', error);
    }
  }

  // 获取音色列表
  fetchVoices()

  // 添加窗口大小变化监听器
  window.addEventListener('resize', handleWindowResize)

  // 初始化窗口大小
  handleWindowResize()
})

// 更新故事
const updateStory = () => {
  // 这里实现故事更新逻辑
  showEditOptions.value = false

  // 更新聊天消息
  chatMessages.value.push({
    type: 'assistant',
    text: '故事设置已更新。您可以继续编辑故事内容或生成视频。',
    status: {
      progress: 100,
      type: 'success',
      text: '更新成功'
    }
  })

  ElMessage.success('故事设置已更新')
}

/**
 * 更新AI创作内容
 * @param {Object} contentData - 要更新的内容数据
 * @param {Number} contentType - 内容类型(10-故事设计,1-故事,2-场景,4-分镜)
 * @param {String} message - 成功消息提示
 * @returns {Promise<boolean>} - 返回是否更新成功
 */
const updateContent = async (contentData, contentType, message = '内容已更新') => {
  if (!conversationId.value) {
    ElMessage.error('会话ID不存在，无法更新内容');
    return false;
  }

  try {
    const response = await updateAiCreationContent(contentData, contentType, conversationId.value);
    if (response.success) {
      ElMessage.success(message);

      if (contentType == 4) { // 分镜修改
        // 如果包含旁白的音频，则需要更新旁白音频
        if (response.data && response.data.length > 0) {
          // 更新分镜
          shotShots.value.shotGroups.forEach((shots, index) => {
            shots.shots.forEach((shot, shotIndex) => {
              const shotData = response.data.find(s => s.shotId == shot.id);
              if (shotData) {
                contentData.shotGroups[index].shots[shotIndex].voice = shotData.audioUrl;
              }
            });
          });
          shotShots.value = contentData;
        }
      }

      return true;
    } else {
      ElMessage.error(response.errMessage || '更新失败');
      return false;
    }
  } catch (error) {
    console.error('更新内容失败:', error);
    ElMessage.error('更新内容失败，请重试');
    return false;
  }
}

// 左右面板状态
const leftPanelCollapsed = ref(localStorage.getItem('leftPanelCollapsed') === 'true')
const rightPanelCollapsed = ref(localStorage.getItem('rightPanelCollapsed') === 'true')
const leftPanelFlex = ref(localStorage.getItem('leftPanelFlex') || '1')
const rightPanelFlex = ref(localStorage.getItem('rightPanelFlex') || '4')
const isResizing = ref(false)

// 初始状态校验：不允许同时隐藏两个面板
if (leftPanelCollapsed.value && rightPanelCollapsed.value) {
  leftPanelCollapsed.value = false
  localStorage.setItem('leftPanelCollapsed', 'false')
}

// 切换左右面板
const toggleLeftPanel = () => {
  // 即使在没有数据时，按钮也会显示（只是禁用状态）
  // 如果右侧已经隐藏，则不允许隐藏左侧
  if (rightPanelCollapsed.value && !leftPanelCollapsed.value) {
    toggleRightPanel();
    return; // 直接返回，不进行任何操作
  }

  leftPanelCollapsed.value = !leftPanelCollapsed.value;
  localStorage.setItem('leftPanelCollapsed', leftPanelCollapsed.value);

  // 恢复默认宽度比例
  leftPanelFlex.value = '1';
  rightPanelFlex.value = '4';
  localStorage.setItem('leftPanelFlex', leftPanelFlex.value);
  localStorage.setItem('rightPanelFlex', rightPanelFlex.value);

  // 添加打印，便于调试
  console.log('切换左侧面板状态:', leftPanelCollapsed.value ? '已收起' : '已展开');
}

const toggleRightPanel = () => {
  // 如果左侧已经隐藏，则不允许隐藏右侧
  if (leftPanelCollapsed.value && !rightPanelCollapsed.value) {
    toggleLeftPanel();
    return; // 直接返回，不进行任何操作
  }

  rightPanelCollapsed.value = !rightPanelCollapsed.value;
  localStorage.setItem('rightPanelCollapsed', rightPanelCollapsed.value);

  // 恢复默认宽度比例
  leftPanelFlex.value = '1';
  rightPanelFlex.value = '4';
  localStorage.setItem('leftPanelFlex', leftPanelFlex.value);
  localStorage.setItem('rightPanelFlex', rightPanelFlex.value);

  // 添加打印，便于调试
  console.log('切换右侧面板状态:', rightPanelCollapsed.value ? '已收起' : '已展开');
}

// 处理面板调整大小
const startResize = (event) => {
  if (leftPanelCollapsed.value || rightPanelCollapsed.value) return;

  isResizing.value = true;
  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);

  // 防止文本选择
  document.body.style.userSelect = 'none';
  document.body.style.pointerEvents = 'none';
};

const handleResize = (event) => {
  if (!isResizing.value) return;

  // 使用requestAnimationFrame优化性能
  requestAnimationFrame(() => {
    const container = document.querySelector('.creation-workspace');
    if (!container) return;

    const containerWidth = container.offsetWidth;
    const mouseX = event.clientX;
    const containerRect = container.getBoundingClientRect();
    const relativeX = mouseX - containerRect.left;

    // 计算分割比例，确保不会太小
    const minWidth = 300; // 最小宽度
    const leftRatio = Math.max(minWidth / containerWidth, Math.min(relativeX / containerWidth, 1 - (minWidth / containerWidth)));
    const rightRatio = 1 - leftRatio;

    // 设置比例
    leftPanelFlex.value = leftRatio.toFixed(2);
    rightPanelFlex.value = rightRatio.toFixed(2);

    // 不在拖动过程中频繁操作localStorage，只在拖动结束时保存
  });
};

const stopResize = () => {
  isResizing.value = false;
  document.removeEventListener('mousemove', handleResize);
  document.removeEventListener('mouseup', stopResize);

  // 恢复文本选择
  document.body.style.userSelect = '';
  document.body.style.pointerEvents = '';

  // 在拖动结束时保存到本地存储
  localStorage.setItem('leftPanelFlex', leftPanelFlex.value);
  localStorage.setItem('rightPanelFlex', rightPanelFlex.value);
};

// 添加outputRatio响应式变量
const outputRatio = ref('16:9') // 默认输出比例为16:9

// 在视频播放相关状态后面添加
// 处理故事设计更新
const handleDesignStoryUpdate = async (updatedDesignStory) => {
  // 更新本地数据
  designStory.value = updatedDesignStory;

  // 发送到服务器
  await updateContent(updatedDesignStory, 10, '故事设计已更新');
}

// 处理故事更新
const handleShotStoryUpdate = async (updatedShotStory) => {
  // 更新本地数据
  shotStory.value = updatedShotStory;

  // 发送到服务器
  await updateContent(updatedShotStory, 1, '故事已更新');
}

// 处理分镜更新
const handleShotShotsUpdate = async (updatedShotShots) => {
  console.log('更新分镜数据:', updatedShotShots?.shotGroups?.length || 0, '个分镜组')
  shotShots.value = updatedShotShots

  // 调用更新API
  try {
    await updateContent(updatedShotShots, 4, '分镜已更新')
  } catch (error) {
    console.error('更新分镜失败:', error)
    ElMessage.error('更新分镜失败')
  }
}

const handleShotRolesUpdate = async (updatedShotRoles) => {
  console.log('更新角色数据:', updatedShotRoles?.characters?.length || 0, '个角色')
  shotRoles.value = updatedShotRoles

  // 调用更新API
  try {
    await updateContent(updatedShotRoles, 3, '角色已更新')
  } catch (error) {
    console.error('更新角色失败:', error)
    ElMessage.error('更新角色失败')
  }
}

// 状态栏Tab定义
const statusTabs = [
  { name: 'session', title: '项目创意', icon: 'Pointer', result: 'chatSessionResult' },
  // { name: 'design', title: '大纲设计', icon: 'SetUp', result: 'designResult' },
  { name: 'edit', title: '角色设计', icon: 'User', result: 'editResult' },
  // { name: 'script', title: '故事设计', icon: 'Document', result: 'scriptResult' },
  { name: 'animation', title: '故事分镜', icon: 'VideoCamera', result: 'animationResult' },
  // { name: 'records', title: '有声故事', icon: 'Collection' }
];

// 处理Tab点击
const handleTabClick = (tabName) => {
  activeTab.value = tabName;
  // 如果右侧面板已收起，则展开它

  // 宽度小于等于 1000px 时，隐藏右侧创作流程
  if (window.innerWidth <= 1000) {
    rightPanelCollapsed.value = true;
    localStorage.setItem('rightPanelCollapsed', rightPanelCollapsed.value);

    // 恢复默认宽度比例
    leftPanelFlex.value = '1';
    rightPanelFlex.value = '3';
    localStorage.setItem('leftPanelFlex', leftPanelFlex.value);
    localStorage.setItem('rightPanelFlex', rightPanelFlex.value);

  } else if (leftPanelCollapsed.value) {
    toggleRightPanel();
  }

};

// 获取对应tab的结果状态
const getTabResult = (tabName) => {
  switch (tabName) {
    case 'session': return chatSessionResult.value || { status: 'completed' };
    case 'design': return designResult.value || { status: '' };
    case 'script': return scriptResult.value || { status: '' };
    case 'edit': return editResult.value || { status: '' };
    // case 'animation': return animationResult.value || { status: '' };
    case 'animation': return { 'status': shotShotsCount.value > 0 ? 'completed' : '' };
    case 'records': return { 'status': recordsCount.value > 0 ? 'completed' : '' };
    default: return { 'status': '' };
  }
};

// 计算总场景数
const getTotalScenes = () => {
  if (!shotScenes.value || !shotScenes.value.scenes) return 0;
  return shotScenes.value.scenes.length;
};

// 计算总镜头数
const getTotalShots = () => {
  if (!shotShots.value || !shotShots.value.shotGroups) return 0;
  return shotShots.value.shotGroups.reduce((total, group) => total + group.totalShots, 0);
};

// 更新记录数量
const updateRecordsCount = (count) => {
  console.log('updateRecordsCount', count)
  recordsCount.value = count
}

// 更新分镜章节数量
const updateShotShotsCount = (count) => {
  console.log('updateShotShotsCount', count)
  shotShotsCount.value = count
}

// 获取作品数量
const getRecordsCount = () => {
  // 默认使用本地记录，但如果右侧组件已更新数量，则使用那个值
  return recordsCount.value || 0
}

// 粒子动画函数
const animateParticles = () => {
  // 预定义一组与设计风格协调的颜色
  const colors = [
    'rgba(99, 102, 241, 0.6)',  // 紫色
    'rgba(79, 70, 229, 0.6)',   // 深紫色
    'rgba(139, 92, 246, 0.6)',   // 紫罗兰色
    'rgba(168, 85, 247, 0.6)',   // 紫红色
    'rgba(59, 130, 246, 0.6)',   // 蓝色
    'rgba(14, 165, 233, 0.6)',   // 天蓝色
    'rgba(6, 182, 212, 0.6)',    // 青色
    'rgba(20, 184, 166, 0.6)',   // 蓝绿色
    'rgba(236, 72, 153, 0.6)',   // 粉色
    'rgba(244, 114, 182, 0.6)',  // 浅粉色
  ];

  // 暗色模式下使用的颜色（稍微亮一些）
  const darkModeColors = [
    'rgba(99, 102, 241, 0.7)',
    'rgba(79, 70, 229, 0.7)',
    'rgba(139, 92, 246, 0.7)',
    'rgba(168, 85, 247, 0.7)',
    'rgba(59, 130, 246, 0.7)',
    'rgba(14, 165, 233, 0.7)',
    'rgba(6, 182, 212, 0.7)',
    'rgba(20, 184, 166, 0.7)',
    'rgba(236, 72, 153, 0.7)',
    'rgba(244, 114, 182, 0.7)',
  ];

  const particles = document.querySelectorAll('.particle');
  particles.forEach((particle, index) => {
    // 设置不同的初始位置和动画延迟
    const x = Math.random() * 90;
    const y = Math.random() * 100;
    const delay = Math.random() * 2;
    const duration = 10 + Math.random() * 10;
    const size = 5 + Math.random() * 30;

    // 随机选择一个颜色
    const colorIndex = Math.floor(Math.random() * colors.length);
    const color = colors[colorIndex];
    const darkColor = darkModeColors[colorIndex];

    // 设置自定义属性，用于暗色模式切换
    particle.setAttribute('data-light-color', color);
    particle.setAttribute('data-dark-color', darkColor);

    // 应用样式
    particle.style.left = `${x}%`;
    particle.style.top = `${y}%`;
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    particle.style.animationDelay = `${delay}s`;
    particle.style.animationDuration = `${duration}s`;
    particle.style.backgroundColor = document.body.classList.contains('dark') ? darkColor : color;
  });
}

// 获取语音列表
const fetchVoices = async () => {
  try {
    isLoadingVoices.value = true
    const response = await getVoiceList()
    if (response.success) {
      // 处理音色数据
      voices.value = response.data
      console.log('获取音色列表成功:', voices.value.length)
    } else {
      console.error('获取音色列表失败:', response.data?.errMessage)
    }
  } catch (error) {
    console.error('获取音色列表异常:', error)
  } finally {
    isLoadingVoices.value = false
  }
}

// 处理窗口大小变化
const handleWindowResize = () => {
  // 在这里添加处理窗口大小变化的逻辑
  console.log('窗口大小变化:', window.innerWidth, window.innerHeight)
}

// 开始进度模拟 - 约3分钟到达99%
const startProgressSimulation = () => {
  // 重置进度
  loadingProgress.value = 0

  // 清除之前的定时器
  if (progressTimer) {
    clearInterval(progressTimer)
  }

  // 3分钟 = 180秒，到达99%
  // 每100ms更新一次，总共1800次更新
  // 每次增加 99/1800 ≈ 0.055%
  const totalTime = 180000 // 3分钟 = 180000毫秒
  const updateInterval = 100 // 每100毫秒更新一次
  const totalUpdates = totalTime / updateInterval // 1800次更新
  const progressIncrement = 99 / totalUpdates // 每次增加的进度

  progressTimer = setInterval(() => {
    if (loadingProgress.value < 99) {
      // 使用非线性增长，开始快，后面慢
      const currentProgress = loadingProgress.value
      let increment = progressIncrement

      // 前30%快速增长
      if (currentProgress < 30) {
        increment = progressIncrement * 1.5
      }
      // 30%-70%正常增长
      else if (currentProgress < 70) {
        increment = progressIncrement * 1.0
      }
      // 70%-90%缓慢增长
      else if (currentProgress < 90) {
        increment = progressIncrement * 0.7
      }
      // 90%-99%非常缓慢增长
      else {
        increment = progressIncrement * 0.3
      }

      
      loadingProgress.value = Math.min(99, loadingProgress.value + increment)// 保留两位小数
      loadingProgress.value = Math.round(loadingProgress.value * 100) / 100 // 保留两位小数
    }
  }, updateInterval)
}

// 停止进度模拟
const stopProgressSimulation = () => {
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
  // 完成时设置为100%
  loadingProgress.value = 100
}

</script>

<style scoped>
.create-page {
  /* min-height: 100vh; */
  /* background-color: #f8f8f8; */
  color: #333;
  transition: background-color 0.3s, color 0.3s;
  position: relative;
  overflow-x: hidden;
  /* backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px); */
}

body.dark .create-page {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.main-content {
  /* max-width: 1600px; */
  margin: 0 auto;
  padding: 0px 0px 0px 0px;
  height: calc(100vh - 50px);
}

.input-section {
  background-color: #f8fafc;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  margin-top: 20px;
  border: 1px solid #e2e8f0;
}

.input-section.minimized {
  padding: 12px;
}

/* 编辑模式状态栏 */
.edit-mode-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #edf2ff;
  border: 1px solid #c7d2fe;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 20px;
  transition: background-color 0.3s, border-color 0.3s, color 0.3s;
}

body.dark .edit-mode-banner {
  background-color: rgba(79, 70, 229, 0.15);
  border-color: rgba(99, 102, 241, 0.3);
  color: var(--primary-color);
}

.edit-info {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #4f46e5;
  font-weight: 500;
  transition: color 0.3s;
}

body.dark .edit-info {
  color: var(--primary-color);
}

.edit-icon {
  font-size: 18px;
}

.edit-actions {
  display: flex;
  gap: 10px;
}

/* 编辑选项面板 */
.edit-options-panel {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  margin-bottom: 20px;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s;
}

body.dark .edit-options-panel {
  background-color: var(--bg-card);
  border-color: var(--border-color);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
}

.edit-options-content {
  padding: 20px;
}

.chat-centent {
  max-height: calc(100vh - 50px);
  height: calc(100vh - 50px);
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 2;
}

.chat-centent-panel {
  min-width: calc(100vw - 400px);
  max-width: 1000px;
  max-height: 700px;
  border-radius: 32px;
  background: rgba(255, 255, 255, 0.8);
  /* backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px); */
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(255, 255, 255, 0.4);
  transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.3s, box-shadow 0.3s, opacity 0.3s, transform 0.3s;
  padding: 20px;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  transform: translateZ(0);
  animation: panel-appear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* 添加卡片装饰元素 */
.chat-centent-panel::before,
.chat-centent-panel::after {
  content: '';
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(138, 92, 246, 0.1));
  filter: blur(20px);
  z-index: -1;
  pointer-events: none;
}

.chat-centent-panel::before {
  top: -20px;
  left: -20px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.1));
}

.chat-centent-panel::after {
  bottom: -30px;
  right: -30px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(168, 85, 247, 0.1));
}

.chat-centent-panel:hover {
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.9),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  transform: translateY(-5px);
}

body.dark .chat-centent-panel {
  background: rgba(0, 0, 0, 0.226);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

body.dark .chat-centent-panel:hover {
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 添加面板出现动画 */
@keyframes panel-appear {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }

  70% {
    opacity: 1;
    transform: translateY(-7px) scale(1.02);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 添加背景渐变 */
.create-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 10% 20%, rgba(204, 205, 240, 0.15) 0%, rgba(217, 209, 234, 0.1) 90%);
  z-index: 0;
  transition: background 0.3s;
}

body.dark .create-page::before {
  background: radial-gradient(circle at 10% 20%, rgba(99, 102, 241, 0.1) 0%, rgba(138, 92, 246, 0.05) 90%);
}

/* 平板设备样式 */
@media (max-width: 1024px) {
  .chat-centent-panel {
    max-width: 90%;
    padding: 35px 30px;
  }
}

/* 移动设备样式 */
/* @media (max-width: 768px) {
  .chat-centent-panel {
    padding: 25px 15px;
    border-radius: 16px;
    max-width: 95%;
  }
} */

/* 小型移动设备样式 */
@media (max-width: 480px) {
  .main-content {
    margin: 0;
  }

  .chat-centent-panel {
    min-width: 300px;

    padding: 20px 12px;
    border-radius: 12px;
    max-width: 100%;
  }
}

/* 创作工作区样式 */
.creation-workspace {
  min-width: 380px;
  max-height: calc(100vh - 50px);
  height: calc(100vh - 50px);
  /* max-height: 100vh; */
  display: flex;
  /* gap: 6px; */
  /* margin-top: 10px; */
  /* border-radius: 4px; */
  position: relative;
  overflow: hidden;
}

.continue-creation {
  position: absolute;
  bottom: 10px;
  left: 10px;
  /* background-color: #f8fafc; */
  border-radius: 4px;
  padding: 4px 10px;
  transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s;
  cursor: pointer;
  color: #4f46e5;
  font-weight: 500;
  transition: color 0.3s;
  border: 1px solid #e2e8f0;
  box-shadow: 0 4px 6px -1px rgba(63, 26, 245, 0.366);
}

body.dark .continue-creation {
  background-color: var(--bg-card);
  border-color: var(--border-color);
  color: var(--text-primary);
  box-shadow: 0 1px 6px 2px rgb(75, 45, 244);
}

/* 中间调整条样式 */
.panel-resizer {
  width: 2px;
  height: 100%;
  /* background-color: #e2e8f04e; */
  cursor: col-resize;
  position: relative;
  /* z-index: 12; */
  transition: background-color 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
}

body.dark .panel-resizer {
  /* background-color: var(--border-color); */
}

.panel-resizer:hover {
  /* background-color: #cbd5e1; */
}

.panel-resizer.collapsed {
  display: none;
}

.resizer-handle {
  width: 1px;
  height: 40px;
  background-color: #94a3b8;
  border-radius: 2px;
  transition: background-color 0.3s, height 0.2s;
}

.panel-resizer:hover .resizer-handle {
  background-color: #036bfde9;
  height: 45%;
  border-radius: 2px;
}

body.dark .resizer-handle {
  background-color: #64748b;
}

body.dark .panel-resizer:hover .resizer-handle {
  /* background-color: #94a3b8; */
}

/* 右侧创作流程 */
.creation-process-container {
  position: relative;
  padding: 0px 0px 0 0px;
  flex: 2;
  transition: opacity 0.3s ease;
  /* background-color: #ffffff; */
  /* border-top: 1px solid #e2e8f0; */
  border-radius: 16px;
  display: flex;
  overflow: hidden;
}

body.dark .creation-process-container {
  /* background-color: var(--bg-card); */
  border-color: var(--border-color);
}

.creation-process-container.collapsed {
  flex: 0;
  min-width: 0px;
  max-width: 0px;
  overflow: hidden;
}

.creation-process-container.collapsed :deep(.creation-process) {
  opacity: 0;
  pointer-events: none;
}

/* 左侧聊天区域 */
.chat-section-container {
  position: relative;
  min-width: 380px;
  flex: 1;
  flex-shrink: 0;
  transition: opacity 0.3s ease;
  overflow: hidden;
  /* border-top: 1px solid #e2e8f0; */
  /* border-right: 1px solid #e2e8f0; */
  display: flex;
  flex-direction: column;
  margin: 6px 0px 6px 6px;
  border-radius: 10px;
  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.2);
  background-color: #ffffff7c;
}

/* 添加拖动状态类，移除过渡动画 */
.creation-workspace.is-resizing .chat-section-container,
.creation-workspace.is-resizing .creation-process-container {
  transition: none;
}

body.dark .chat-section-container {
  background-color: #16161879;
  border-color: var(--border-color);
  box-shadow: 0 0 10px 0 #3a78ff7e;
}

.chat-header {
  /* padding: 13.5px 16px; */
  height: 50px;
  padding: 4px 16px;
  /* border-bottom: 1px solid #e2e8f0; */
  transition: border-color 0.3s;
}

body.dark .chat-header {
  border-color: var(--border-color);
}

.chat-section-container.collapsed {
  flex: 0;
  min-width: 0px;
  max-width: 0px;
  overflow: hidden;
}

.chat-section-container.collapsed .chat-section,
.chat-section-container.collapsed .chat-header {
  opacity: 0;
  pointer-events: none;
}

/* 聊天区域内容 */
.chat-section {
  /* height: 100%; */
  flex: 1;
  width: 100%;
  overflow: hidden;
  transition: opacity 0.2s ease;
}

/* 切换按钮 */
.panel-toggle {
  position: absolute;
  z-index: 1;
  width: 24px;
  height: 48px;
  background-color: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 24px 0 0 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0;
  color: #606266;
  transition: all 0.2s ease, background-color 0.3s, border-color 0.3s, color 0.3s;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  outline: none;
}

body.dark .panel-toggle {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

.panel-toggle:hover {
  color: #409eff;
  background-color: #f0f9ff;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.15);
}

body.dark .panel-toggle:hover {
  color: var(--primary-color);
  background-color: var(--bg-hover);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
}

.panel-toggle.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f7fa;
  color: #c0c4cc;
  box-shadow: none;
  /* 确保按钮始终显示，即使是禁用状态 */
}

body.dark .panel-toggle.disabled:hover {
  color: var(--text-tertiary);
  background-color: rgba(42, 42, 42, 0.7);
  transform: none;
}

.panel-toggle:active:not(.disabled) {
  transform: translateY(-50%) scale(0.95);
}

/* 左侧面板按钮 */
.left-toggle {
  top: 50%;
  transform: translateY(-50%);
  left: -0px;
  border-radius: 0 24px 24px 0;
  display: flex !important;
  /* 强制显示 */
}

.left-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

/* 右侧面板按钮 */
.right-toggle {
  top: 50%;
  transform: translateY(-50%);
  right: 0px;
  border-radius: 24px 0 0 24px;
}

.right-toggle:active {
  transform: translateY(-50%) scale(0.95);
}

/* 历史记录区域样式 */
.history-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .history-section {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.history-section-header {
  margin-top: 40px;
  margin-bottom: 16px;
}

.history-title {
  font-size: 20px;
  font-weight: 600;
  color: #3f4146;
  margin: 0;
  transition: color 0.3s;
}

body.dark .history-title {
  color: var(--text-primary);
}

.history-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.history-item {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease, background-color 0.3s, border-color 0.3s;
}

body.dark .history-item {
  background-color: var(--bg-card);
  border-color: var(--border-color);
}

.history-item:hover {
  border-color: #cbd5e1;
  background-color: #f8fafc;
}

body.dark .history-item:hover {
  border-color: var(--border-color-hover);
  background-color: var(--bg-hover);
}

.video-preview {
  position: relative;
  aspect-ratio: 16/9;
}

.video-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  color: white;
}

body.dark .video-overlay {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
}

.video-info {
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  transition: background-color 0.3s;
}

body.dark .video-info {
  background-color: var(--bg-card);
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #1e293b;
  transition: color 0.3s;
}

body.dark .video-title {
  color: var(--text-primary);
}

.video-time {
  font-size: 12px;
  color: #64748b;
  transition: color 0.3s;
}

body.dark .video-time {
  color: var(--text-tertiary);
}

.video-actions {
  margin: 8px 12px 12px;
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* 响应式调整 */
@media (max-width: 1000px) {

  /* 左侧聊天区域 */
  .chat-section-container {
    width: 100%;
    min-width: 100%;
  }

  /* 右侧创作流程 */
  .creation-process-container {
    width: 100%;
    min-width: 0px;
  }
}

/* 补充修复 panel-toggle.disabled:hover 样式 */
.panel-toggle.disabled:hover {
  color: #c0c4cc;
  background-color: #f5f7fa;
  transform: none;
}

body.dark .panel-toggle.disabled:hover {
  color: var(--text-tertiary);
  background-color: rgba(42, 42, 42, 0.7);
  transform: none;
}

/* 修复标题样式 */
.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20px;
  transition: color 0.3s;
}

body.dark .page-title {
  color: var(--text-primary);
}

.page-status-bar {
  display: flex;
  justify-content: space-between;
  /* align-items: center; */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  /* background-color: #ffffff; */
  /* border-bottom: 1px solid #e2e8f0; */
  height: auto;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  transition: background-color 0.3s, border-color 0.3s;
}

body.dark .page-status-bar {
  /* background-color: var(--bg-card); */
  /* border-color: var(--border-color); */
}

.status-tabs {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
  overflow: hidden;
  box-sizing: border-box;
  transition: all 0.25s ease;
  position: relative;
  z-index: 1;
}

.text-content {
  font-size: 16px;
  font-weight: 600;
  color: #3b83f6;
}

.status-tab-item {
  display: flex;
  flex-direction: column;
  border-radius: 30px;
  padding: 8px 12px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.25s ease;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  opacity: 1;
}

.status-tab-item:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
  opacity: 1;
}

/* 活跃状态样式 */
.status-tab-item.active {
  background-color: #f0f7ff;
  border-color: #a5d8ff;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.12);
  /* 左侧添加指示条 */
  border-left: 2px solid #1890ff;
  opacity: 1;
}

/* 已完成状态样式 */
.status-tab-item.completed {
  background-color: #f0f7ff;
  border-color: #bae6fd;
  opacity: 1;
}

.status-tab-item.completed .tab-icon {
  background: linear-gradient(135deg, #3a78ff 0%, #1860d6 100%);
  color: white;
}

.status-tab-item.completed .tab-title {
  color: #1860d6;
  font-weight: 600;
  opacity: 1;
}

.status-tab-item.completed.active {
  /* background-color: #ecf6ff; */
  border-color: #1890ff;
  /* border-left: 6px solid #1890ff; */
  border-right: 8px solid #1890ff;
}

body.dark .status-tab-item {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

body.dark .status-tab-item:hover {
  background-color: var(--bg-hover);
  border-color: var(--border-color-hover);
}

body.dark .status-tab-item.active {
  background-color: rgba(64, 158, 255, 0.08);
  border-color: rgba(64, 158, 255, 0.3);
  border-left: 2px solid #1890ff;
}

body.dark .status-tab-item.completed {
  background-color: rgba(24, 144, 255, 0.08);
  border-color: rgba(24, 144, 255, 0.3);
}

body.dark .status-tab-item.completed.active {
  background-color: rgba(24, 144, 255, 0.12);
  border-color: #1890ff;
  /* border-left: 6px solid #1890ff; */
  border-right: 8px solid #1890ff;
}

body.dark .status-tab-item.completed .tab-title {
  color: #1890ff;
}

.status-tab-item.completed .summary-text {
  color: #1860d6;
}

body.dark .summary-text {
  color: #94a3b8;
}

body.dark .status-tab-item.completed .summary-text {
  color: #69b1ff;
}

.status-tab-item .status-icon.completed {
  color: #1890ff;
  font-weight: bold;
}

.status-tab-item.completed .status-icon.completed {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  padding: 2px;
}

.tab-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-tab-item .tab-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(99, 102, 241, 0.04) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6366f1;
  font-size: 16px;
  transition: all 0.25s ease;
  flex-shrink: 0;
}

.status-tab-item.active .tab-icon {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
}

.tab-icon svg {
  width: 18px;
  height: 18px;
}

.el-icon-setup::before,
.el-icon-document::before,
.el-icon-user::before,
.el-icon-video-camera::before,
.el-icon-collection::before,
.el-icon-check::before,
.el-icon-loading::before {
  display: none;
}

.tab-content {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
  gap: 8px;
  overflow: hidden;
  min-width: 200px;
}

.status-tab-item .tab-title {
  font-size: 14px;
  font-weight: 600;
  color: #4b5563;
  margin: 0;
  transition: color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.6;
}

.status-tab-item.active .tab-title {
  color: #3a78ff;
  font-weight: 600;
}

body.dark .status-tab-item .tab-title {
  color: #e5e7eb;
}

body.dark .status-tab-item.active .tab-title {
  color: #3a78ff;
}

body.dark .status-tab-item.completed .tab-title {
  color: #1890ff;
}

.tab-summary {
  font-size: 12px;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

body.dark .tab-summary {
  color: #94a3b8;
}

.summary-text {
  font-size: 12px;
  color: #64748b;
}

.status-tab-item.completed .summary-text {
  color: #1861d6c8;
}

body.dark .summary-text {
  color: #94a3b8;
}

body.dark .status-tab-item.completed .summary-text {
  color: #69b2ff9b;
}

.status-tab-item .tab-status {
  margin-left: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-tab-item .status-icon {
  font-size: 16px;
  line-height: 1;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-tab-item .status-icon.completed {
  color: #1890ff;
  font-weight: bold;
}

.status-tab-item .status-icon.in-progress {
  color: #f59e0b;
  animation: rotating 2s linear infinite;
}

.status-tab-item.completed .status-icon.completed {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
  border-radius: 50%;
  padding: 2px;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 响应式样式 */
/* @media (max-width: 768px) {
  .page-status-bar {
    padding: 8px;
  }
  
  .status-tabs {
    gap: 4px;
  }
  
  .status-tab-item {
    padding: 6px 8px;
  }
  
  .status-tab-item .tab-icon {
    width: 24px;
    height: 24px;
    font-size: 14px;
  }
  
  .status-tab-item .tab-title {
    font-size: 13px;
  }
  
  .tab-summary,
  .summary-text {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .status-tab-item {
    padding: 5px 6px;
  }
  
  .status-tab-item .tab-icon {
    width: 22px;
    height: 22px;
    font-size: 13px;
  }
  
  .status-tab-item .tab-title {
    font-size: 12px;
  }
  
  .tab-summary,
  .summary-text {
    font-size: 10px;
  }
} */

/* 添加"生成故事"和"生成分镜"按钮的样式 */
.tab-next-button {
  margin-left: auto;
  opacity: 1;
}

.tab-next-button span {
  display: inline-block;
  padding: 4px 10px;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
  white-space: nowrap;
  text-align: center;
}

.tab-next-button span:hover {
  box-shadow: 0 3px 6px rgba(99, 102, 241, 0.3);
  background: linear-gradient(135deg, #818cf8 0%, #6366f1 100%);
}

.tab-next-button span:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

/* 暗黑模式下的样式 */
body.dark .tab-next-button span {
  background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
  box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
}

body.dark .tab-next-button span:hover {
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  box-shadow: 0 3px 6px rgba(79, 70, 229, 0.4);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tab-next-button span {
    padding: 3px 8px;
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .tab-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 0px;
  }

  .tab-next-button {
    margin-top: 4px;
    margin-left: 0;
  }

  .tab-next-button span {
    padding: 2px 6px;
    font-size: 10px;
  }
}

/* 添加粒子动画容器 */
.particles-container {
  position: fixed;
  /* 改为fixed，使背景在滚动时保持在视口中 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: radial-gradient(circle at 10% 20%, rgba(204, 205, 240, 0.15) 0%, rgba(217, 209, 234, 0.1) 90%); */
  z-index: 0;
}

.particle {
  position: absolute;
  border-radius: 50%;
  animation: floatAnimation linear infinite;
  opacity: 0;
  transition: background-color 0.3s;
}

@keyframes floatAnimation {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0;
  }

  20% {
    opacity: 0.5;
  }

  50% {
    opacity: 0.8;
  }

  80% {
    opacity: 0.5;
  }

  100% {
    transform: translateY(-60vh) translateX(20vw) rotate(360deg);
    opacity: 0;
  }
}

/* 移动设备隐藏粒子效果以提高性能 */
@media (max-width: 480px) {
  .particles-container {
    display: none;
  }
}

/* 添加chat-loading相关样式 */
.chat-loading {
  min-width: calc(100vw - 400px);
  max-width: 1000px;
  height: calc(100vh - 400px);
  border-radius: 32px;
  /* background: rgba(255, 255, 255, 0.8); */
  /* backdrop-filter: blur(10px); */
  /* -webkit-backdrop-filter: blur(10px); */
  border: none;
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(255, 255, 255, 0.4); */
  transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.3s, box-shadow 0.3s;
  padding: 40px;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
  transform: translateZ(0);
  animation: panel-appear 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

body.dark .chat-loading {
  background: rgba(0, 0, 0, 0.226);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.chat-loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 300px;
}

.chat-loading .loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(99, 102, 241, 0.2);
  border-radius: 50%;
  border-top-color: #6366f1;
  animation: spin 1.2s linear infinite;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
}

/* 添加脉冲效果 */
.loading-pulse {
  position: absolute;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(99, 101, 241, 0.199);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  z-index: 1;
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(0.6);
    opacity: 0.2;
  }

  50% {
    transform: scale(1.4);
    opacity: 0.5;
  }
}

body.dark .loading-pulse {
  background: rgba(99, 102, 241, 0.15);
}

/* 装饰元素样式 */
.loading-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.decoration-item {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4), rgba(168, 85, 247, 0.2));
  /* filter: blur(20px); */
  opacity: 0.5;
  animation: float 10s ease-in-out infinite;
}

.decoration-item.item-1 {
  width: 120px;
  height: 120px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.decoration-item.item-2 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 15%;
  background: linear-gradient(225deg, rgba(59, 130, 246, 0.4), rgba(99, 102, 241, 0.2));
  animation-delay: 2s;
}

.decoration-item.item-3 {
  width: 80px;
  height: 80px;
  bottom: 15%;
  left: 20%;
  background: linear-gradient(45deg, rgba(236, 72, 153, 0.4), rgba(139, 92, 246, 0.2));
  animation-delay: 4s;
}

body.dark .decoration-item {
  opacity: 0.25;
}

body.dark .decoration-item.item-1 {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(168, 85, 247, 0.1));
}

body.dark .decoration-item.item-2 {
  background: linear-gradient(225deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.1));
}

body.dark .decoration-item.item-3 {
  background: linear-gradient(45deg, rgba(236, 72, 153, 0.3), rgba(139, 92, 246, 0.1));
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0) scale(1);
  }

  50% {
    transform: translateY(-20px) scale(1.05);
  }
}

body.dark .chat-loading .loading-spinner {
  border-color: rgba(99, 102, 241, 0.1);
  border-top-color: var(--primary-color);
}

.chat-loading .loading-text {
  font-size: 26px;
  font-weight: 800;
  color: #4f46e5;
  margin-bottom: 16px;
  transition: color 0.3s;
}

body.dark .chat-loading .loading-text {
  color: var(--primary-color);
}

.chat-loading .loader-dots {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-loading .loader-dots span {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #6366f1;
  animation: loader-dots 1.4s ease-in-out infinite;
}

.chat-loading .loader-dots span:nth-child(1) {
  animation-delay: 0s;
}

.chat-loading .loader-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.chat-loading .loader-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

body.dark .chat-loading .loader-dots span {
  background-color: var(--primary-color);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes loader-dots {

  0%,
  100% {
    transform: scale(0.6);
    opacity: 0.4;
  }

  50% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 响应式调整 - chat-loading */
@media (max-width: 1024px) {
  .chat-loading {
    max-width: 90%;
    padding: 30px;
  }
}

/* @media (max-width: 768px) {
  .chat-loading {
    max-width: 95%;
    padding: 25px;
    border-radius: 20px;
  }

  .chat-loading .loading-spinner {
    width: 50px;
    height: 50px;
    margin-bottom: 20px;
  }

  .chat-loading .loading-text {
    font-size: 16px;
    margin-bottom: 14px;
  }

  .chat-loading .loader-dots span {
    width: 8px;
    height: 8px;
  }
} */

@media (max-width: 480px) {
  .chat-loading {
    padding: 20px;
    border-radius: 16px;
    min-height: 250px;
  }

  .chat-loading-container {
    min-height: 250px;
  }

  .chat-loading .loading-spinner {
    width: 40px;
    height: 40px;
    margin-bottom: 16px;
  }

  .chat-loading .loading-text {
    font-size: 15px;
    margin-bottom: 12px;
  }

  .chat-loading .loader-dots span {
    width: 6px;
    height: 6px;
  }
}

/* 进度条样式 */
.progress-container {
  width: 100%;
  max-width: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: rgba(99, 102, 241, 0.2);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #6366f1, #8b5cf6);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #6366f1;
  transition: color 0.3s;
}

body.dark .progress-bar {
  background-color: rgba(99, 102, 241, 0.1);
}

body.dark .progress-text {
  color: var(--primary-color);
}

/* .page-status-bar {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background-color: #ffffff;
  height: auto;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
  transition: background-color 0.3s, border-color 0.3s;
} */

/* 动态背景效果 */
.background-animation {
  position: fixed;
  /* 改为fixed，使背景在滚动时保持在视口中 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* background: radial-gradient(circle at 10% 20%, rgba(204, 205, 240, 0.15) 0%, rgba(217, 209, 234, 0.1) 90%); */
  z-index: 0;
  /* z-index: 0; */
  opacity: 0.85;
  pointer-events: none;
  /* 确保不会拦截点击事件 */
}

.light-effect {
  position: absolute;
  border-radius: 50%;
  /* filter: blur(60px); */
  opacity: 0.9;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-direction: normal;
}

.light-effect-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, #6365f1b0, #3b82f6b0);
  top: -150px;
  left: -150px;
  animation: moveLight1 12s infinite linear;
}

.light-effect-2 {
  width: 500px;
  height: 500px;
  background: linear-gradient(135deg, #8b5cf6b0, #6366f1b0);
  bottom: -250px;
  right: -250px;
  animation: moveLight2 15s infinite linear;
}

.light-effect-3 {
  width: 600px;
  height: 600px;
  background: linear-gradient(135deg, #3b82f6b0, #0fcbb5b0);
  top: 10%;
  left: 40%;
  animation: moveLight3 10s infinite linear;
}

body.dark .light-effect-1 {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4), rgba(59, 130, 246, 0.4));
}

body.dark .light-effect-2 {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(99, 102, 241, 0.4));
}

body.dark .light-effect-3 {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(26, 241, 216, 0.4));
}

@keyframes moveLight1 {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }

  25% {
    transform: translate(50%, 40%) scale(1.2);
    opacity: 0.6;
  }

  50% {
    transform: translate(100%, 80%) scale(1.3);
    opacity: 0.6;
  }

  75% {
    transform: translate(150%, 50%) scale(1.2);
    opacity: 0.5;
  }

  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }
}

@keyframes moveLight2 {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }

  33% {
    transform: translate(-30%, -25%) scale(1.1);
    opacity: 0.6;
  }

  66% {
    transform: translate(-60%, -50%) scale(1.2);
    opacity: 0.6;
  }

  100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }
}

@keyframes moveLight3 {
  0% {
    transform: translate(-50%, 0) scale(1);
    opacity: 0.5;
  }

  20% {
    transform: translate(-70%, 15%) scale(1.05);
    opacity: 0.6;
  }

  40% {
    transform: translate(-100%, 30%) scale(1.1);
    opacity: 0.6;
  }

  60% {
    transform: translate(-50%, 40%) scale(1.05);
    opacity: 0.6;
  }

  80% {
    transform: translate(0%, 20%) scale(1);
    opacity: 0.5;
  }

  100% {
    transform: translate(-50%, 0) scale(1);
    opacity: 0.5;
  }
}

/* 在小屏设备上减少动效，提高性能 */
/* @media (max-width: 768px) {
  .light-effect {
    filter: blur(30px);
  }

  .light-effect-1 {
    width: 200px;
    height: 200px;
  }

  .light-effect-2 {
    width: 300px;
    height: 300px;
  }

  .light-effect-3 {
    width: 250px;
    height: 250px;
  }
} */

:deep(.creation-tabs) {
  height: 100%;
  position: relative;
  /* 确保tabs在背景动效之上 */
  /* z-index: 1; */
  /* tabs 移动到顶部 */
  /* display: flex;
  flex-direction: column; */
  /* background-color: #000000; */
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
  /* border-bottom: 1px solid #e4e7ed; */
  /* background-color: #f8fafc; */
  position: relative;
  transition: all 0.3s ease;
  opacity: 1;
  max-height: var(--tab-height, 72px);
  overflow: hidden;
}
</style>