<template>
  <div class="local-image-inpainting" v-if="visible">
    <div class="inpainting-backdrop" @click="handleClose"></div>
    <div class="inpainting-container">
      <div class="inpainting-header">
        <h3>智能修图</h3>
        <div class="header-info">
          <!-- <span class="mask-info">彩色区域为重绘区域</span> -->
        </div>
        <el-icon class="close-icon" @click="handleClose">
          <Close />
        </el-icon>
      </div>

      <div class="inpainting-content">
        <!-- 左侧：画布区域 -->
        <div class="inpainting-left-panel">
          <div class="canvas-container" v-loading="loading">
            <div class="canvas-wrapper">
              <!-- 图片预览层 -->
              <img :src="imageUrl" alt="原图" v-if="imageUrl" class="original-image" ref="originalImage" />

              <!-- 绘制层 -->
              <canvas ref="maskCanvas" class="mask-canvas" @mousedown="startDrawing"
                @mousemove="(e) => { draw(e); updateCursorPosition(e); }" @mouseup="stopDrawing"
                @mouseleave="stopDrawing" @touchstart="startDrawingTouch" @touchmove="drawTouch" @touchend="stopDrawing"
                @touchcancel="stopDrawing"></canvas>

              <!-- 自定义光标 -->
              <div class="custom-cursor" ref="customCursor"></div>
            </div>
          </div>
        </div>

        <!-- 右侧：控制面板 -->
        <div class="inpainting-right-panel">
          <!-- 遮罩预览区域 -->
          <div class="mask-preview-section" v-if="false">
            <div class="tool-actions"
              style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
              <div class="section-title">遮罩预览</div>

            </div>
            <div class="mask-preview-container">
              <img :src="maskPreviewUrl" v-if="maskPreviewUrl" class="mask-preview-image" alt="遮罩预览" />
              <div v-else class="mask-preview-placeholder">
                <span>在左侧画布上绘制遮罩区域</span>
              </div>
            </div>
          </div>

          <div class="inpainting-controls">
            <div class="canvas-tools">
              <div class="tool-group">
                <span class="tool-label">涂抹工具</span>
                <div class="tool-selector">
                  <div class="tool-button" :class="{ active: toolMode === 'brush' }" @click="toolMode = 'brush'">
                    <el-icon>
                      <Brush />
                    </el-icon>
                    <span>画笔</span>
                  </div>
                  <div class="tool-button" :class="{ active: toolMode === 'eraser' }" @click="toolMode = 'eraser'">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    <span>橡皮擦</span>
                  </div>
                </div>
              </div>

              <div class="tool-group">
                <span class="tool-label">
                  {{ toolMode === 'brush' ? '笔刷大小' : '橡皮擦大小' }}
                  <span style="color: #909399;">{{ brushSize }}px</span>
                </span>
                <el-slider v-model="brushSize" :min="5" :max="200" :step="1" :disabled="loading" />
              </div>

              <div class="tool-group" v-if="false">
                <span class="tool-label">笔刷颜色</span>
                <div class="color-selector">
                  <div v-for="(color, index) in brushColors" :key="index" class="color-option"
                    :class="{ active: brushColor === color.value }" :style="{ backgroundColor: color.value }"
                    @click="brushColor = color.value" :title="color.name"></div>
                </div>
              </div>

            </div>


            <div class="tool-actions" style="margin-right: 10px;">
            <el-button type="primary" size="small" class="action-btn undo-btn" @click="undoLastDraw"
              :disabled="drawActions.length === 0 || loading">
              <el-icon>
                <Back />
              </el-icon>
              <span>撤销</span>
            </el-button>
            <el-button type="danger" size="small" class="action-btn clear-btn" @click="clearCanvas" :disabled="loading">
              <el-icon>
                <Delete />
              </el-icon>
              <span>清除</span>
            </el-button>
          </div>

            <div class="section-title">
              <!-- <span>重绘提示词</span> -->
            </div>
            <div class="prompt-input">
              <el-input v-model="inpaintPrompt" type="textarea" :autosize="{ minRows: 5, maxRows: 8 }"
                placeholder="描述你想修改的内容..." :disabled="loading" class="prompt-textarea" />
            </div>

            <div class="action-buttons">

              <!-- 如果有涂抹区域，显示重绘区域按钮 -->
              <button v-if="hasMaskArea" class="custom-button primary-button" @click="applyInpainting"
                :disabled="!canApplyInpainting || loading" :class="{ 'disabled': !canApplyInpainting || loading }">
                <el-icon>
                  <Brush />
                </el-icon>
                <span>重绘区域（消耗30积分）</span>
              </button>

              <!-- 如果没有涂抹区域，显示智能修图按钮 -->
              <button v-else class="custom-button primary-button" @click="smartEditImage"
                :disabled="!canSmartEdit || loading" :class="{ 'disabled': !canSmartEdit || loading }">
                <el-icon>
                  <Brush />
                </el-icon>
                <span>智能修图（消耗30积分）</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { Picture, Close, Delete, Brush, Back } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { uploadToOSS } from '@/api/oss.js';
import { submitImageInpainting, getImageInpaintingResult } from '@/api/image.js';
import { generateCanvasImage } from '@/api/auth.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  imageUrl: {
    type: String,
    default: ''
  },
  conversationId: {
    type: String,
    default: ''
  },
  shotId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close', 'update']);

// 提示词
const inpaintPrompt = ref('');

// 加载状态
const loading = ref(false);

// 画布相关
const originalImage = ref(null);
const maskCanvas = ref(null);
const maskCtx = ref(null);
const isDrawing = ref(false);
const lastX = ref(0);
const lastY = ref(0);
const brushSize = ref(20);
const drawActions = ref([]);
const canvasInfo = ref(null);
const customCursor = ref(null); // 自定义光标引用

// 添加工具模式
const toolMode = ref('brush'); // 'brush' 或 'eraser'

// 画笔颜色相关
const brushColors = [
  { name: '红色', value: 'rgba(255, 0, 0, 0.4)' },
  { name: '蓝色', value: 'rgba(0, 0, 255, 0.4)' },
  { name: '绿色', value: 'rgba(0, 255, 0, 0.4)' },
  { name: '黄色', value: 'rgba(255, 255, 0, 0.4)' }
];
const brushColor = ref(brushColors[0].value);

// 是否有涂抹区域
const hasMaskArea = computed(() => {
  return drawActions.value.length > 0;
});

// 是否可以应用重绘
const canApplyInpainting = computed(() => {
  return inpaintPrompt.value.trim() !== '' && drawActions.value.length > 0;
});

// 是否可以智能修图
const canSmartEdit = computed(() => {
  return inpaintPrompt.value.trim() !== '' && drawActions.value.length === 0;
});

// 轮询相关
const currentInpaintingCode = ref('');
const pollingInterval = ref(3000);
const pollingTimer = ref(null);
const maxPollingCount = ref(60); // 最多轮询60次，即3分钟
const pollingCount = ref(0);

// 初始化画布
const initCanvas = () => {
  if (!maskCanvas.value || !props.imageUrl) return;

  console.log('初始化画布开始');

  // 等待原始图片加载完成
  const waitForOriginalImage = () => {
    if (!originalImage.value) {
      console.log('原始图片元素不存在，等待DOM更新');
      setTimeout(waitForOriginalImage, 50);
      return;
    }

    if (!originalImage.value.complete) {
      console.log('原始图片尚未加载完成，等待加载');
      originalImage.value.onload = initCanvasAfterImageLoaded;
      return;
    }

    initCanvasAfterImageLoaded();
  };

  // 图片加载完成后初始化画布
  const initCanvasAfterImageLoaded = () => {
    if (!originalImage.value) {
      console.error('原始图片元素不存在');
      return;
    }

    console.log('原始图片尺寸:', originalImage.value.naturalWidth, 'x', originalImage.value.naturalHeight);
    console.log('显示尺寸:', originalImage.value.clientWidth, 'x', originalImage.value.clientHeight);

    // 使用图片的实际显示尺寸
    const displayWidth = originalImage.value.clientWidth;
    const displayHeight = originalImage.value.clientHeight;

    // 设置canvas的CSS尺寸与图片显示尺寸相同
    maskCanvas.value.style.width = `${displayWidth}px`;
    maskCanvas.value.style.height = `${displayHeight}px`;

    // 获取设备像素比
    const dpr = window.devicePixelRatio || 1;
    console.log('设备像素比:', dpr);

    // 设置canvas的实际尺寸（考虑设备像素比以提高清晰度）
    maskCanvas.value.width = displayWidth * dpr;
    maskCanvas.value.height = displayHeight * dpr;

    // 获取画布上下文
    maskCtx.value = maskCanvas.value.getContext('2d');

    // 缩放上下文以匹配设备像素比
    maskCtx.value.scale(dpr, dpr);

    // 设置画布背景为透明
    maskCtx.value.clearRect(0, 0, maskCanvas.value.width, maskCanvas.value.height);

    // 保存画布信息
    canvasInfo.value = {
      width: displayWidth,
      height: displayHeight,
      dpr: dpr,
      originalWidth: originalImage.value.naturalWidth,
      originalHeight: originalImage.value.naturalHeight
    };

    // 清空绘画历史
    drawActions.value = [];

    // 确保画布可见
    maskCanvas.value.style.opacity = '1';
    maskCanvas.value.style.pointerEvents = 'auto';

    console.log('画布初始化完成');
  };

  // 开始等待原始图片
  waitForOriginalImage();
};

// 开始绘制（鼠标）
const startDrawing = (e) => {
  console.log('开始绘制', e.type);
  isDrawing.value = true;

  // 保存当前画布状态，用于撤销
  saveCanvasState();

  // 获取鼠标相对于画布的位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const canvasX = (e.clientX - rect.left);
  const canvasY = (e.clientY - rect.top);

  console.log('鼠标位置:', canvasX, canvasY);

  lastX.value = canvasX;
  lastY.value = canvasY;

  // 单点绘制，避免零长度线段
  drawDot(canvasX, canvasY);
};

// 绘制（鼠标）
const draw = (e) => {
  if (!isDrawing.value) return;

  // 获取鼠标当前位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const canvasX = (e.clientX - rect.left);
  const canvasY = (e.clientY - rect.top);

  console.log('绘制中', canvasX, canvasY);

  // 绘制线条
  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  // 更新上一次位置
  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 开始绘制（触摸）
const startDrawingTouch = (e) => {
  isDrawing.value = true;

  // 阻止默认行为（如滚动）
  e.preventDefault();

  // 保存当前画布状态，用于撤销
  saveCanvasState();

  // 获取触摸点相对于画布的位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];
  const canvasX = (touch.clientX - rect.left);
  const canvasY = (touch.clientY - rect.top);

  lastX.value = canvasX;
  lastY.value = canvasY;

  // 单点绘制，避免零长度线段
  drawDot(canvasX, canvasY);
};

// 绘制（触摸）
const drawTouch = (e) => {
  if (!isDrawing.value) return;

  // 阻止默认行为（如滚动）
  e.preventDefault();

  // 获取触摸点当前位置
  const rect = maskCanvas.value.getBoundingClientRect();
  const touch = e.touches[0];
  const canvasX = (touch.clientX - rect.left);
  const canvasY = (touch.clientY - rect.top);

  // 绘制线条
  drawLine(lastX.value, lastY.value, canvasX, canvasY);

  // 更新上一次位置
  lastX.value = canvasX;
  lastY.value = canvasY;
};

// 单点绘制
const drawDot = (x, y) => {
  if (!maskCtx.value) {
    console.error('画布上下文不存在');
    return;
  }

  console.log('绘制点', x, y);

  // 保存当前上下文状态
  maskCtx.value.save();

  if (toolMode.value === 'brush') {
    // 绘制模式
    // 设置合成模式为"xor"，这样重叠区域不会增加透明度
    maskCtx.value.globalCompositeOperation = 'xor';

    // 使用固定不透明度的红色
    maskCtx.value.fillStyle = brushColor.value;
  } else {
    // 橡皮擦模式
    maskCtx.value.globalCompositeOperation = 'destination-out';
    maskCtx.value.fillStyle = 'rgba(0, 0, 0, 1)';
  }

  // 绘制一个圆点
  maskCtx.value.beginPath();
  maskCtx.value.arc(x, y, brushSize.value / 2, 0, Math.PI * 2);
  maskCtx.value.fill();
  maskCtx.value.closePath();

  // 恢复上下文状态
  maskCtx.value.restore();
};

// 绘制线条
const drawLine = (x1, y1, x2, y2) => {
  if (!maskCtx.value) {
    console.error('画布上下文不存在');
    return;
  }

  console.log('绘制线条', x1, y1, 'to', x2, y2);

  // 保存当前上下文状态
  maskCtx.value.save();

  if (toolMode.value === 'brush') {
    // 绘制模式
    // 设置合成模式为"xor"，这样重叠区域不会增加透明度
    maskCtx.value.globalCompositeOperation = 'xor';
    maskCtx.value.strokeStyle = brushColor.value;
  } else {
    // 橡皮擦模式
    maskCtx.value.globalCompositeOperation = 'destination-out';
    maskCtx.value.strokeStyle = 'rgba(0, 0, 0, 1)';
  }

  maskCtx.value.lineWidth = brushSize.value;
  maskCtx.value.lineCap = 'round';
  maskCtx.value.lineJoin = 'round';

  // 绘制线条
  maskCtx.value.beginPath();
  maskCtx.value.moveTo(x1, y1);
  maskCtx.value.lineTo(x2, y2);
  maskCtx.value.stroke();
  maskCtx.value.closePath();

  // 恢复上下文状态
  maskCtx.value.restore();
};

// 停止绘制
const stopDrawing = () => {
  if (isDrawing.value) {
    console.log('停止绘制');
    // 绘制结束时更新预览
    updateMaskPreview();
  }
  isDrawing.value = false;
};

// 保存画布状态
const saveCanvasState = () => {
  if (!maskCanvas.value) return;
  drawActions.value.push(maskCanvas.value.toDataURL());
};

// 撤销最后一次绘制
const undoLastDraw = () => {
  if (drawActions.value.length === 0) return;

  // 移除最后一次绘制的状态
  drawActions.value.pop();

  // 清空画布
  clearCanvas();

  // 如果还有之前的绘制操作，恢复到上一个状态
  if (drawActions.value.length > 0) {
    const lastState = drawActions.value[drawActions.value.length - 1];
    const img = new Image();
    img.onload = () => {
      if (!canvasInfo.value) return;

      // 绘制上一个状态
      maskCtx.value.drawImage(img, 0, 0, canvasInfo.value.width, canvasInfo.value.height);
      // 更新预览
      updateMaskPreview();
    };
    img.src = lastState;
  } else {
    // 如果没有历史状态，直接更新预览（显示空白遮罩）
    updateMaskPreview();
  }
};

// 清除画布
const clearCanvas = () => {
  if (!maskCanvas.value || !canvasInfo.value) return;

  // 清空画布
  maskCtx.value.clearRect(0, 0, maskCanvas.value.width / canvasInfo.value.dpr, maskCanvas.value.height / canvasInfo.value.dpr);

  // 重置绘画历史
  drawActions.value = [];

  // 更新预览
  updateMaskPreview();
};

// 生成遮罩图片（用于上传和API调用）
const generateMaskImage = async () => {
  if (!maskCanvas.value || !canvasInfo.value) return null;

  console.log('生成遮罩图片 - 画布信息:', {
    canvasWidth: maskCanvas.value.width,
    canvasHeight: maskCanvas.value.height,
    displayWidth: canvasInfo.value.width,
    displayHeight: canvasInfo.value.height,
    originalWidth: canvasInfo.value.originalWidth,
    originalHeight: canvasInfo.value.originalHeight,
    dpr: canvasInfo.value.dpr
  });

  // 创建临时画布，确保与原图分辨率完全一致
  const tempCanvas = document.createElement('canvas');
  tempCanvas.width = canvasInfo.value.originalWidth;
  tempCanvas.height = canvasInfo.value.originalHeight;
  const tempCtx = tempCanvas.getContext('2d');

  // 设置高质量渲染
  tempCtx.imageSmoothingEnabled = true;
  tempCtx.imageSmoothingQuality = 'high';

  // 清空临时画布，设置为黑色背景
  tempCtx.fillStyle = 'black';
  tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);

  // 计算缩放比例，确保精确映射到原图尺寸
  const scaleX = canvasInfo.value.originalWidth / canvasInfo.value.width;
  const scaleY = canvasInfo.value.originalHeight / canvasInfo.value.height;

  // 将当前画布内容精确缩放到原始图片尺寸
  // 画布的实际尺寸是 width * dpr 和 height * dpr
  tempCtx.drawImage(
    maskCanvas.value,
    0, 0, maskCanvas.value.width, maskCanvas.value.height,
    0, 0, canvasInfo.value.originalWidth, canvasInfo.value.originalHeight
  );

  // 获取图像数据进行二值化处理
  const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
  const data = imageData.data;

  // 将有颜色的区域转换为白色，其他区域保持黑色
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const a = data[i + 3];

    // 如果像素有颜色且不透明度大于阈值（表示被画笔标记）
    if (a > 50 && (r > 50 || g > 50 || b > 50)) {
      // 设置为白色（遮罩区域）
      data[i] = 255;     // R
      data[i + 1] = 255; // G
      data[i + 2] = 255; // B
      data[i + 3] = 255; // A (完全不透明)
    } else {
      // 设置为黑色（非遮罩区域）
      data[i] = 0;       // R
      data[i + 1] = 0;   // G
      data[i + 2] = 0;   // B
      data[i + 3] = 255; // A (完全不透明)
    }
  }

  // 将处理后的图像数据放回画布
  tempCtx.putImageData(imageData, 0, 0);

  // 将画布内容转换为Blob对象
  return new Promise((resolve) => {
    tempCanvas.toBlob((blob) => {
      resolve(blob);
    }, 'image/png', 1.0); // 使用最高质量
  });
};

// 生成预览用的遮罩图片（用于右侧面板显示）
const generatePreviewMaskImage = async () => {
  if (!maskCanvas.value || !canvasInfo.value) return null;

  // 创建预览画布，使用较小的尺寸以提高性能
  const previewCanvas = document.createElement('canvas');
  const previewWidth = 300; // 固定预览宽度
  const aspectRatio = canvasInfo.value.originalHeight / canvasInfo.value.originalWidth;
  const previewHeight = Math.round(previewWidth * aspectRatio);

  previewCanvas.width = previewWidth;
  previewCanvas.height = previewHeight;
  const previewCtx = previewCanvas.getContext('2d');

  // 设置高质量渲染
  previewCtx.imageSmoothingEnabled = true;
  previewCtx.imageSmoothingQuality = 'high';

  // 清空预览画布，设置为黑色背景
  previewCtx.fillStyle = 'black';
  previewCtx.fillRect(0, 0, previewWidth, previewHeight);

  // 将当前画布内容缩放到预览尺寸
  previewCtx.drawImage(
    maskCanvas.value,
    0, 0, maskCanvas.value.width, maskCanvas.value.height,
    0, 0, previewWidth, previewHeight
  );

  // 获取图像数据进行二值化处理
  const imageData = previewCtx.getImageData(0, 0, previewWidth, previewHeight);
  const data = imageData.data;

  // 将有颜色的区域转换为白色，其他区域保持黑色
  for (let i = 0; i < data.length; i += 4) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const a = data[i + 3];

    // 如果像素有颜色且不透明度大于阈值
    if (a > 50 && (r > 50 || g > 50 || b > 50)) {
      // 设置为白色（遮罩区域）
      data[i] = 255;     // R
      data[i + 1] = 255; // G
      data[i + 2] = 255; // B
      data[i + 3] = 255; // A
    } else {
      // 设置为黑色（非遮罩区域）
      data[i] = 0;       // R
      data[i + 1] = 0;   // G
      data[i + 2] = 0;   // B
      data[i + 3] = 255; // A
    }
  }

  // 将处理后的图像数据放回画布
  previewCtx.putImageData(imageData, 0, 0);

  // 将画布内容转换为Blob URL用于预览
  return new Promise((resolve) => {
    previewCanvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        resolve(url);
      } else {
        resolve(null);
      }
    }, 'image/png', 0.8); // 预览使用较低质量以提高性能
  });
};

// 遮罩预览相关
const maskBlob = ref(null);
const maskPreviewUrl = ref(null);

// 更新遮罩预览
const updateMaskPreview = async () => {
  try {
    // 清理之前的预览URL
    if (maskPreviewUrl.value) {
      URL.revokeObjectURL(maskPreviewUrl.value);
    }

    // 生成新的预览
    const previewUrl = await generatePreviewMaskImage();
    maskPreviewUrl.value = previewUrl;
  } catch (error) {
    console.error('更新遮罩预览失败:', error);
  }
};

// 智能修图
const smartEditImage = async () => {
  if (!canSmartEdit.value || loading.value) return;

  loading.value = true;

  try {
    // 处理参考图URL，去除协议和域名部分
    let referenceImageUrl = props.imageUrl || '';
    // 检查是否以 http:// 或 https:// 开头
    if (/^https?:\/\//.test(referenceImageUrl)) {
      // 去除协议和域名部分
      referenceImageUrl = referenceImageUrl.replace(/^https?:\/\/[^\/]+/, '');
    }

    // 准备API参数，参考 ImageGenerationPanel.vue 中的 generateImage 函数
    const params = {
      prompt: inpaintPrompt.value.trim(),
      referenceImageUrl: referenceImageUrl,
      aspectRatio: '16:9', // 默认比例，可以根据需要调整
      strength: 5.0, // 默认强度，可以根据需要调整
      shotId: props.shotId || ''
    };

    console.log('提交智能修图请求:', params);

    // 调用API
    ElMessage.info('正在生成图片，请稍候...');
    const response = await generateCanvasImage(params);

    if (response.success) {
      ElMessage.success('图片生成任务已提交');
      // 通知父组件更新
      emit('update');
      // 关闭弹窗
      handleClose();
    } else {
      throw new Error(response.errMessage || '图片生成失败');
    }
  } catch (error) {
    console.error('智能修图失败:', error);
    ElMessage.error('智能修图失败，请重试');
    loading.value = false;
  }
};

// 应用局部重绘
const applyInpainting = async () => {
  if (!canApplyInpainting.value || loading.value) return;

  loading.value = true;

  try {
    // 生成遮罩图片
    const generatedMaskBlob = await generateMaskImage();

    if (!generatedMaskBlob) {
      throw new Error('生成遮罩图片失败');
    }

    maskBlob.value = generatedMaskBlob;

    // 上传遮罩图片到OSS
    const maskResult = await uploadToOSS(maskBlob.value, props.conversationId || 'inpainting');
    if (!maskResult || !maskResult.url) {
      throw new Error('上传遮罩图片失败');
    }

    // 构建请求参数
    const requestData = {
      originalImageUrl: props.imageUrl,
      maskImageUrl: maskResult.url,
      prompt: inpaintPrompt.value.trim(),
      shotId: props.shotId || '',
    };

    console.log('提交局部重绘请求:', requestData);

    // 提交局部重绘请求
    const result = await submitImageInpainting(requestData);

    if (result) {
      handleClose();
    } else {
      throw new Error('提交局部重绘请求失败，未获取到处理码');
    }
  } catch (error) {
    console.error('局部重绘失败:', error);
    ElMessage.error('局部重绘失败，请重试');
    loading.value = false;
  }
};



// 关闭组件
const handleClose = () => {
  // 清除轮询定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }

  // 清理预览URL
  if (maskPreviewUrl.value) {
    URL.revokeObjectURL(maskPreviewUrl.value);
    maskPreviewUrl.value = null;
  }

  // 重置状态
  loading.value = false;
  inpaintPrompt.value = '';
  drawActions.value = [];
  currentInpaintingCode.value = '';
  maskBlob.value = null;

  // 通知父组件关闭
  emit('close');
};

// 监听可见性变化
watch(() => props.visible, (newVisible) => {
  console.log('可见性变化', newVisible);
  if (newVisible) {
    // 组件显示时初始化画布
    setTimeout(() => {
      console.log('延时初始化画布');
      initCanvas();
    }, 100);
  }
});

// 监听图片URL变化
watch(() => props.imageUrl, (newUrl) => {
  console.log('图片URL变化', newUrl);
  if (props.visible && newUrl) {
    // 图片URL变化时重新初始化画布
    setTimeout(() => {
      console.log('图片变化，重新初始化画布');
      initCanvas();
    }, 100);
  }
});

// 更新光标位置和大小
const updateCursorPosition = (e) => {
  if (!customCursor.value || !maskCanvas.value) return;

  const rect = maskCanvas.value.getBoundingClientRect();
  const x = e.clientX;
  const y = e.clientY;

  // 更新光标位置
  customCursor.value.style.left = `${x}px`;
  customCursor.value.style.top = `${y}px`;

  // 检查鼠标是否在画布上
  const isOnCanvas =
    x >= rect.left &&
    x <= rect.right &&
    y >= rect.top &&
    y <= rect.bottom;

  // 显示/隐藏光标
  customCursor.value.style.display = isOnCanvas ? 'block' : 'none';
};

// 更新光标大小和样式
const updateCursorSize = () => {
  if (!customCursor.value) return;

  // 设置光标大小为笔刷大小
  const size = brushSize.value;
  customCursor.value.style.width = `${size}px`;
  customCursor.value.style.height = `${size}px`;

  // 根据工具模式设置不同的光标样式
  if (toolMode.value === 'brush') {
    customCursor.value.style.border = '2px solid #fff';
    customCursor.value.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
    customCursor.value.style.boxShadow = '0 0 0 1px rgba(0, 0, 0, 0.5)';
  } else {
    customCursor.value.style.border = '2px solid #000';
    customCursor.value.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
    customCursor.value.style.boxShadow = '0 0 0 1px rgba(255, 255, 255, 0.5)';
  }
};

// 监听笔刷大小变化
watch(() => brushSize.value, () => {
  updateCursorSize();
});

// 监听工具模式变化
watch(() => toolMode.value, () => {
  updateCursorSize();
});

// 组件挂载时
onMounted(() => {
  console.log('组件挂载');
  if (props.visible && props.imageUrl) {
    // 初始化画布
    setTimeout(() => {
      console.log('挂载时初始化画布');
      initCanvas();
      // 初始化光标大小
      updateCursorSize();
    }, 100);
  }

  // 添加鼠标移出窗口事件监听
  window.addEventListener('mouseout', handleMouseOut);
});

// 处理鼠标移出窗口
const handleMouseOut = (e) => {
  if (!customCursor.value) return;

  // 如果鼠标移出窗口，隐藏光标
  if (e.relatedTarget === null) {
    customCursor.value.style.display = 'none';
  }
};

// 组件卸载前
onBeforeUnmount(() => {
  // 清除轮询定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
  }

  // 移除鼠标移出窗口事件监听
  window.removeEventListener('mouseout', handleMouseOut);
});
</script>

<style scoped>
.local-image-inpainting {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

:deep(.el-loading-mask) {
  z-index: 2;
  background-color: rgba(52, 52, 52, 0.788);
  opacity: 1;
  backdrop-filter: blur(3px);
}

.inpainting-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.634);
  backdrop-filter: blur(4px);
}

.inpainting-container {
  position: relative;
  /* width: 90%; */
  max-width: 1400px;
  max-height: 800px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  z-index: 1;
  overflow: hidden;
}

body.dark .inpainting-container {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
}

.inpainting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
}

.header-info {
  flex: 1;
  text-align: center;
  margin: 0 20px;
}

.mask-info {
  font-size: 14px;
  color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

body.dark .inpainting-header {
  border-bottom-color: var(--border-color);
}

.inpainting-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.close-icon {
  cursor: pointer;
  font-size: 20px;
  color: #909399;
}

.close-icon:hover {
  color: #409eff;
}

.inpainting-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.inpainting-left-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* padding: 12px; */
  overflow: hidden;
  border-right: 1px solid #e4e7ed;
}

body.dark .inpainting-left-panel {
  border-right-color: var(--border-color);
}

.canvas-container {
  position: relative;
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  /* border-radius: 8px; */
}

body.dark .canvas-container {
  background-color: var(--bg-secondary);
}

.canvas-wrapper {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.original-image {
  display: block;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  pointer-events: none;
  /* 确保图片不接收鼠标事件 */
}

.mask-canvas {
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: none;
  /* 隐藏原始光标 */
  z-index: 1;
  /* 确保画布在图片上层 */
  pointer-events: auto;
  /* 确保画布接收鼠标事件 */
}

/* 自定义光标样式 */
.custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.5);
  pointer-events: none;
  transform: translate(-50%, -50%);
  z-index: 100;
  display: none;
}

.canvas-tools {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

body.dark .canvas-tools {
  background-color: var(--bg-secondary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tool-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tool-selector {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.tool-button {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tool-button:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.tool-button:last-child {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.tool-button.active {
  background-color: #409eff;
  color: white;
  border-color: #409eff;
}

.tool-button:not(.active):hover {
  background-color: #f5f7fa;
}

body.dark .tool-button {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

body.dark .tool-button.active {
  background-color: #409eff;
  color: white;
}

body.dark .tool-button:not(.active):hover {
  background-color: var(--bg-secondary);
}

.tool-label {
  text-align: left;
  font-size: 14px;
  color: #606266;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

body.dark .tool-label {
  color: var(--text-secondary);
}

.color-selector {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  padding: 4px;
  border-radius: 6px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.2s ease;
}

.color-option:hover {
  border-color: #409eff;
}

.color-option.active {
  border-color: #409eff;
  box-shadow: 0 0 4px rgba(64, 158, 255, 0.5);
}

body.dark .color-selector {
  /* background-color: var(--bg-secondary); */
}

.tool-actions {
  display: flex;
  gap: 6px;
  justify-content: flex-end;
  align-items: center;
}

:deep(.el-button+.el-button) {
  margin-left: 0px;
}

/* 优化按钮样式 */
.action-btn {
  min-width: 80px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.08);
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  transition: box-shadow 0.2s, background 0.2s;
}

.action-btn .el-icon {
  font-size: 16px;
}

.action-btn.clear-btn {
  background: #fff0f0;
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

.action-btn.clear-btn:hover:not([disabled]) {
  background: #f56c6c;
  color: #fff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.15);
}

.action-btn.undo-btn {
  background: #f4f8ff;
  color: #409eff;
  border: 1px solid #409eff;
}

.action-btn.undo-btn:hover:not([disabled]) {
  background: #409eff;
  color: #fff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.action-btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

body.dark .action-btn.clear-btn {
  background: #2a1a1a;
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

body.dark .action-btn.clear-btn:hover:not([disabled]) {
  background: #f56c6c;
  color: #fff;
}

body.dark .action-btn.undo-btn {
  background: #1a2233;
  color: #409eff;
  border: 1px solid #409eff;
}

body.dark .action-btn.undo-btn:hover:not([disabled]) {
  background: #409eff;
  color: #fff;
}

.inpainting-right-panel {
  width: 240px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  gap: 12px;
}

/* 遮罩预览区域 */
.mask-preview-section {
  margin-bottom: 10px;
}

.mask-preview-container {
  width: 100%;
  aspect-ratio: 16 / 9;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f7fa;
  overflow: hidden;
}

body.dark .mask-preview-container {
  border-color: var(--border-color);
  background-color: var(--bg-secondary);
}

.mask-preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 4px;
}

.mask-preview-placeholder {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 20px;
}

body.dark .mask-preview-placeholder {
  color: var(--text-secondary);
}

.inpainting-controls {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.section-title {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  text-align: left;
}

body.dark .section-title {
  color: var(--text-primary);
}

body.dark .custom-cursor {
  border-color: #fff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.7);
}

.prompt-input {
  margin-bottom: 4px;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 8px 8px;
  font-size: 15px;
  min-height: 80px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.04);
  transition: border 0.2s, box-shadow 0.2s;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  border-radius: 20px;
  overflow: hidden;
}

.custom-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.primary-button {
  background-color: #409eff;
  color: white;
}

.primary-button:hover {
  background-color: #66b1ff;
}

.primary-button.disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

body.dark .primary-button {
  background-color: var(--primary-color);
}

body.dark .primary-button:hover {
  background-color: var(--primary-color-hover);
}

body.dark .primary-button.disabled {
  background-color: var(--primary-color-disabled);
}

/* 优化重绘提示词输入框样式 */
.prompt-textarea .el-textarea__inner {
  border-radius: 12px !important;
  border: 1.5px solid #e4e7ed;
  background: #f8fafc;
  padding: 12px 14px;
  font-size: 15px;
  min-height: 80px;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.04);
  transition: border 0.2s, box-shadow 0.2s;
}

.prompt-textarea .el-textarea__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.12);
  background: #fff;
}

body.dark .prompt-textarea .el-textarea__inner {
  background: #23272e;
  border-color: #333a45;
  color: #e0e6ed;
}

body.dark .prompt-textarea .el-textarea__inner:focus {
  border-color: #409eff;
  background: #23272e;
}
</style>