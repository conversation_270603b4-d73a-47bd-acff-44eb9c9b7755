<template>
  <div class="audio-generation-dialog" v-if="modelValue">
    <!-- 遮罩层 -->
    <div class="dialog-backdrop" @click="handleBackdropClick"></div>
    
    <!-- 弹框内容 -->
    <div class="dialog-container" @click.stop>
      <!-- 标题栏 -->
      <div class="dialog-header">
        <h3 class="dialog-title">音频生成</h3>
        <button class="close-button" @click="handleClose">×</button>
      </div>

      <!-- 内容区域 -->
      <div class="dialog-content">
<!--        <el-tabs v-model="activeTab" class="generation-tabs asset-selector-tabs" @tab-click="handleTabChange">-->
<!--          <el-tab-pane label="音色生成" name="audio"></el-tab-pane>-->
          <!-- 音效暂时不可用 提示-->
          <!-- <el-tab-pane label="音效" name="effect" disabled></el-tab-pane> -->
<!--        </el-tabs>-->

        <!-- 内容区域 -->
        <div class="generation-content-audio">
          <!-- 旁白生成 tab -->
          <div v-if="activeTab === 'audio'" class="tab-content">
            <!-- 添加新音频 -->
            <div class="panel-section">

              <!-- 音色选择 -->
              <div class="form-item voice-selector-item">
                <DropdownPanel v-model="showVoiceSelector" position="bottom" align="center" closeOnClickOutside :width="900"
                  :maxHeight="420" :zIndex="1200" class="voice-selector-dropdown" dropdown-id="audio-voice-selector">
                  <!-- 触发器插槽 -->
                  <template #trigger>
                    <div class="voice-selector-trigger">
                      <div class="selected-voice">
                        {{selectedVoiceId ? (voices.find(v => v.id == selectedVoiceId)?.name || '请选择音色') : '请选择音色'}}
                      </div>
                      <el-icon class="voice-selector-icon">
                        <ArrowDown />
                      </el-icon>
                    </div>
                  </template>

                  <!-- 下拉内容插槽 -->
                  <div class="voice-selector-container">
                    <VoiceSelector :voices="voices" :selectedVoice="selectedVoiceId ? Number(selectedVoiceId) : null" :isLoading="isLoadingVoices"
                      @update:selectedVoice="handleVoiceSelect" @refresh-voices="refreshVoices" />
                  </div>
                </DropdownPanel>
              </div>
              <!-- <div class="form-label">新增音频</div> -->
              <div class="form-item">
                <div class="image-generation-textarea">
                  <!-- 文本输入区域 -->
                  <div class="text-input-container">
                    <!-- 多音字高亮显示层 -->
                    <div class="text-highlight-overlay" v-if="newVoice.text && pronunciationEntries.length > 0">
                      <div class="highlight-content" v-html="highlightedText"></div>
                    </div>

                    <el-input v-model="newVoice.text" type="textarea" placeholder="请输入文本"
                      :autosize="{ minRows: 3, maxRows: 6 }"
                      class="text-input-field" />
                  </div>
                </div>
                <div class="character-count">{{ newVoice.text?.length || 0 }}/100</div>

              </div>

              <div class="form-item">

                <!-- 多音字设置按钮 -->
                <div class="pronunciation-setting-button" @click="openPronunciationDialog">
                  <el-icon><Setting /></el-icon>
                  <span>多音字设置</span>
                  <span class="pronunciation-count" v-if="pronunciationEntries.length > 0">({{ pronunciationEntries.length }})</span>
                </div>
              </div>

              <!-- 情感选择 -->
              <div class="form-item voice-param-item">
                <!-- <div class="voice-param-header">
                  <div class="voice-param-label">情感</div>
                  <div class="voice-param-value">{{ emotionLabels[newVoice.emotion] || '无' }}</div>
                </div> -->
                <el-select v-model="newVoice.emotion" class="emotion-selector" placeholder="情感（无）">
                  <el-option v-for="(label, value) in emotionLabels" :key="value" :label="label" :value="value" />
                </el-select>
              </div>


              <!-- 语速控制 -->
              <div class="form-item voice-param-item">
                <div class="voice-param-header">
                  <div class="voice-param-label">语速（取值越大，语速越快）</div>
                  <div class="voice-param-value">{{ newVoice.speed.toFixed(1) }}</div>
                </div>
                <el-slider v-model="newVoice.speed" :min="0.5" :max="2" :step="0.1"
                  :format-tooltip="(val) => val.toFixed(1)" />
              </div>

              <!-- 音量控制 -->
              <div class="form-item voice-param-item">
                <div class="voice-param-header">
                  <div class="voice-param-label">音量（取值越大，音量越高）</div>
                  <div class="voice-param-value">{{ newVoice.vol.toFixed(1) }}</div>
                </div>
                <el-slider v-model="newVoice.vol" :min="0.1" :max="10" :step="0.1"
                  :format-tooltip="(val) => val.toFixed(1)" />
              </div>

              <!-- 语调控制 -->
              <div class="form-item voice-param-item">
                <div class="voice-param-header">
                  <div class="voice-param-label">语调（0为原音色输出）</div>
                  <div class="voice-param-value">{{ newVoice.pitch }}</div>
                </div>
                <el-slider v-model="newVoice.pitch" :min="-12" :max="12" :step="1" :format-tooltip="(val) => val" />
              </div>

              <div class="panel-action-audio">
                <div class="back-button generation-button" @click="generateAudio(newVoice.text, selectedVoiceId)"
                  :class="{ 'is-generating': isGeneratingAudio }">
                  <el-icon class="ml-5" v-if="!isGeneratingAudio">
                    <Microphone />
                  </el-icon>
                  <el-icon class="loading-icon ml-5" v-else>
                    <svg class="circular-icon" viewBox="0 0 50 50">
                      <circle class="path" cx="25" cy="25" r="20" fill="none" />
                    </svg>
                  </el-icon>
                  <span>{{ isGeneratingAudio ? '生成中...' : '生成音频' }}</span>
                  <span class="points-cost" v-if="!isGeneratingAudio">(1积分)</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 音效生成 tab -->
          <div v-if="activeTab === 'effect'" class="tab-content">
            <div class="panel-section">

              <!-- 音效类型选择 -->
              <div class="form-item voice-param-item">
                <el-select v-model="newSoundEffect.type" class="emotion-selector" placeholder="音效类型">
                  <el-option v-for="(label, value) in soundEffectTypes" :key="value" :label="label" :value="value" />
                </el-select>
              </div>

              <!-- 音效描述输入 -->
              <div class="form-item" v-if="newSoundEffect.type === 'custom'">
                <div class="image-generation-textarea">
                  <el-input v-model="newSoundEffect.description" type="textarea" placeholder="请描述您想要的音效，例如：雨声、鸟叫声、汽车引擎声等"
                    :autosize="{ minRows: 3, maxRows: 6 }" />
                </div>
                <div class="character-count">{{ newSoundEffect.description?.length || 0 }}/100</div>
              </div>

              <!-- 音效时长控制 -->
              <div class="form-item voice-param-item">
                <div class="voice-param-header">
                  <div class="voice-param-label">音效时长（秒）</div>
                  <div class="voice-param-value">{{ newSoundEffect.duration.toFixed(1) }}</div>
                </div>
                <el-slider v-model="newSoundEffect.duration" :min="0.1" :max="10" :step="0.1"
                  :format-tooltip="(val) => val.toFixed(1)" />
              </div>

              <!-- 音量控制 -->
              <div class="form-item voice-param-item">
                <div class="voice-param-header">
                  <div class="voice-param-label">音量（取值越大，音量越高）</div>
                  <div class="voice-param-value">{{ newSoundEffect.volume.toFixed(1) }}</div>
                </div>
                <el-slider v-model="newSoundEffect.volume" :min="0.1" :max="2.0" :step="0.1"
                  :format-tooltip="(val) => val.toFixed(1)" />
              </div>
            </div>

            <div class="panel-action-audio">
              <div class="back-button generation-button" @click="generateSoundEffect"
                :class="{ 'is-generating': isGeneratingSoundEffect }">
                <el-icon class="ml-5" v-if="!isGeneratingSoundEffect">
                  <Microphone />
                </el-icon>
                <el-icon class="loading-icon ml-5" v-else>
                  <svg class="circular-icon" viewBox="0 0 50 50">
                    <circle class="path" cx="25" cy="25" r="20" fill="none" />
                  </svg>
                </el-icon>
                <span>{{ isGeneratingSoundEffect ? '生成中...' : '生成音效' }}</span>
                <span class="points-cost" v-if="!isGeneratingSoundEffect">(2积分)</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 多音字设置弹框 -->
    <div class="pronunciation-dialog" v-if="showPronunciationDialog">
      <div class="dialog-backdrop" @click="closePronunciationDialog"></div>
      <div class="dialog-container pronunciation-dialog-container" @click.stop>
        <div class="dialog-header">
          <h3 class="dialog-title">多音字设置</h3>
          <button class="close-button" @click="closePronunciationDialog">×</button>
        </div>

        <div class="dialog-content">
          <div class="pronunciation-description">
            <p><strong>多音字设置说明：</strong></p>
            <p>• 英文示例：<code>omg/oh my god</code>，<code>AI/artificial intelligence</code></p>
            <p>• 中文示例：<code>文字/注音</code>，如 <code>燕少飞/(yan4)(shao3)(fei1)</code></p>
            <p>• 中文声调：<code>1-阴平、2-阳平、3-上声、4-去声、5-轻声</code></p>
          </div>

          <!-- 添加新条目 -->
          <div class="pronunciation-add-section">
            <div class="pronunciation-input-row">
              <el-input
                v-model="newPronunciationEntry.word"
                placeholder="输入文字"
                class="pronunciation-word-input"
              />
              <span class="pronunciation-separator">/</span>
              <el-input
                v-model="newPronunciationEntry.pronunciation"
                placeholder="输入注音"
                class="pronunciation-pronunciation-input"
              />
              <el-button type="primary" @click="addPronunciationEntry" :disabled="!canAddEntry">添加</el-button>
            </div>
          </div>

          <!-- 已添加的条目列表 -->
          <div class="pronunciation-list" v-if="pronunciationEntries.length > 0">
            <!-- <div class="pronunciation-list-header">已设置的多音字 ({{ pronunciationEntries.length }})</div> -->
            <div class="pronunciation-item" v-for="(entry, index) in pronunciationEntries" :key="index">
              <span class="pronunciation-entry-text">{{ entry.word }}/{{ entry.pronunciation }}</span>
              <el-button type="danger" size="small" @click="removePronunciationEntry(index)">删除</el-button>
            </div>
          </div>

          <!-- <div class="pronunciation-dialog-actions">
            <el-button @click="closePronunciationDialog">取消</el-button>
            <el-button type="primary" @click="savePronunciationSettings">确定</el-button>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';
import { Microphone, ArrowDown, Setting } from '@element-plus/icons-vue';
import { ElMessage, ElLoading } from 'element-plus';
import DropdownPanel from '../parent/DropdownPanel.vue';
import VoiceSelector from '../selector/VoiceSelector.vue';
import { createCanvasShotAudio } from '@/api/auth.js';

// 组件属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  shot: {
    type: Object,
    required: true
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  },
  voiceToCopy: {
    type: Object,
    default: null
  }
});

// 事件
const emit = defineEmits(['update:modelValue', 'refresh-canvas', 'refresh-voices']);

// 当前活动的tab
const activeTab = ref('audio');

// 音色选择相关状态
const selectedVoiceId = ref(null);
const showVoiceSelector = ref(false);

// 情感选项
const emotionLabels = {
  undefined: "无",
  "happy": "开心",
  "sad": "悲伤",
  "angry": "愤怒",
  "fearful": "恐惧",
  "disgusted": "厌恶",
  "surprised": "惊讶",
  "neutral": "平静"
};

// 音效类型选项
const soundEffectTypes = {
  "custom": "自定义",
  "nature": "自然音效",
  "urban": "城市音效",
  "mechanical": "机械音效",
  "musical": "音乐音效",
  "ambient": "环境音效",
  "impact": "撞击音效",
  "electronic": "电子音效",
  "voice": "人声音效"
};

// 新音频数据
const newVoice = ref({
  name: '',
  text: '',
  style: 'natural',
  audioDuration: 0,
  speed: 1.0,    // 语速，范围[0.5,2]，默认值为1.0
  vol: 1.0,      // 音量，范围(0,10]，默认值为1.0
  pitch: 0,      // 语调，范围[-12,12]，默认值为0（整数）
  emotion: undefined,    // 情感，枚举值 ["", "happy", "sad", "angry", "fearful", "disgusted", "surprised", "neutral"]
  tone: []  // 多音字数组，格式：["燕少飞/(yan4)(shao3)(fei1)","达菲/(da2)(fei1)","omg/oh my god"]
});

// 多音字设置相关状态
const showPronunciationDialog = ref(false);
const pronunciationEntries = ref([]);
const newPronunciationEntry = ref({ word: '', pronunciation: '' });

// 新音效数据
const newSoundEffect = ref({
  description: '',
  duration: 3.0,    // 音效时长，范围[1,10]，默认值为3.0秒
  volume: 1.0,      // 音量，范围[0.1,2.0]，默认值为1.0
  type: 'custom'   // 音效类型
});

// 音频生成状态
const isGeneratingAudio = ref(false);

// 音效生成状态
const isGeneratingSoundEffect = ref(false);

// 处理音色选择
const handleVoiceSelect = (voiceId) => {
  selectedVoiceId.value = voiceId;
  showVoiceSelector.value = false;
};

// 刷新音色列表
const refreshVoices = () => {
  // 触发事件，让父组件刷新音色列表
  emit('refresh-voices');
};

// 多音字设置相关方法
const openPronunciationDialog = () => {
  // 从当前设置中加载已有的多音字条目
  pronunciationEntries.value = [...newVoice.value.tone.map(entry => {
    const [word, pronunciation] = entry.split('/');
    return { word, pronunciation };
  })];
  showPronunciationDialog.value = true;
};

const closePronunciationDialog = () => {
  showPronunciationDialog.value = false;
  // 重置新条目输入
  newPronunciationEntry.value = { word: '', pronunciation: '' };
};

const canAddEntry = computed(() => {
  return newPronunciationEntry.value.word.trim() && newPronunciationEntry.value.pronunciation.trim();
});

// 计算高亮显示的文本
const highlightedText = computed(() => {
  if (!newVoice.value.text || pronunciationEntries.value.length === 0) {
    return newVoice.value.text;
  }

  let text = newVoice.value.text;

  // 按词长度排序，优先匹配长词
  const sortedEntries = [...pronunciationEntries.value].sort((a, b) => b.word.length - a.word.length);

  sortedEntries.forEach(entry => {
    const word = entry.word;
    const pronunciation = entry.pronunciation;

    // 使用正则表达式全局替换，添加高亮标签
    const regex = new RegExp(escapeRegExp(word), 'g');
    text = text.replace(regex, `<span class="pronunciation-highlight" title="${word}/${pronunciation}">${word}</span>`);
  });

  return text;
});

// 转义正则表达式特殊字符
const escapeRegExp = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

const addPronunciationEntry = () => {
  if (!canAddEntry.value) return;

  const word = newPronunciationEntry.value.word.trim();
  const pronunciation = newPronunciationEntry.value.pronunciation.trim();

  // 检查是否已存在相同的词
  const exists = pronunciationEntries.value.some(entry => entry.word === word);
  if (exists) {
    ElMessage.warning('该词已存在，请勿重复添加');
    return;
  }

  pronunciationEntries.value.push({ word, pronunciation });
  newPronunciationEntry.value = { word: '', pronunciation: '' };

  // 保存多音字设置
  savePronunciationSettings();
};

const removePronunciationEntry = (index) => {
  pronunciationEntries.value.splice(index, 1);
  // 保存多音字设置
  savePronunciationSettings();
};

const savePronunciationSettings = () => {
  // 将条目转换为API需要的格式
  newVoice.value.tone = pronunciationEntries.value.map(entry =>
    `${entry.word}/${entry.pronunciation}`
  );
  // closePronunciationDialog();
  ElMessage.success(`多音字设置已保存 (${pronunciationEntries.value.length}个)`);
};

// 处理标签页点击事件
const handleTabChange = (tab) => {
  // 处理标签页点击逻辑
  console.log(`Tab ${tab.name} clicked`);
};

// 关闭弹框
const handleClose = () => {
  if (isGeneratingAudio.value || isGeneratingSoundEffect.value) return;
  emit('update:modelValue', false);
};

// 点击遮罩层关闭弹框
const handleBackdropClick = () => {
  if (showVoiceSelector.value) {
    showVoiceSelector.value = false
  } else if (!isGeneratingAudio.value && !isGeneratingSoundEffect.value){
    // handleClose();
  }
};

// 生成音频
const generateAudio = async (text, voiceId) => {
  // 如果已经在生成中，则不重复操作
  if (isGeneratingAudio.value) {
    console.log('正在生成音频，请稍后再试');
    return;
  }

  if (!text) {
    ElMessage.warning('请输入文本');
    return;
  }

  if (!voiceId) {
    ElMessage.warning('请选择音色');
    return;
  }

  if (!props.shot.id) {
    ElMessage.warning('分镜ID不存在，无法创建音频');
    return;
  }

  try {
    // 设置生成状态为true
    isGeneratingAudio.value = true;

    // 构建API参数
    const params = {
      shotId: props.shot.id,
      text: text,
      voiceId: voiceId,
      audioType: 1,
      speed: newVoice.value.speed,
      vol: newVoice.value.vol,
      pitch: newVoice.value.pitch,
      emotion: newVoice.value.emotion, // 添加情感参数
      tone: newVoice.value.tone // 添加多音字参数
    };

    // 调用API创建音频
    const response = await createCanvasShotAudio(params);

    if (response.success) {
      ElMessage.success('音频生成成功');

      // 清空表单
      newVoice.value = {
        name: '',
        text: '',
        style: 'natural',
        audioDuration: 0,
        speed: 1.0,    // 重置为默认值
        vol: 1.0,      // 重置为默认值
        pitch: 0,      // 重置为默认值
        emotion: undefined,    // 重置为默认值
        tone: []  // 重置多音字设置
      };

      // 重置多音字相关状态
      pronunciationEntries.value = [];
      newPronunciationEntry.value = { word: '', pronunciation: '' };

      // 通知父组件更新画布详情
      emit('refresh-canvas');

      // 设置生成状态为false，然后关闭弹窗
      isGeneratingAudio.value = false;

      // 关闭弹窗
      emit('update:modelValue', false);

      return true;
    } else {
      ElMessage.error(`音频生成失败: ${response.errMessage}`);
    }
  } catch (error) {
    console.error('生成音频异常:', error);
    ElMessage.error('生成音频出错，请稍后再试');
  } finally {
    // 无论成功还是失败，最终都将生成状态设置为false
    isGeneratingAudio.value = false;
  }
  return false;
};

// 生成音效
const generateSoundEffect = async () => {
  // 如果已经在生成中，则不重复操作
  if (isGeneratingSoundEffect.value) {
    console.log('正在生成音效，请稍后再试');
    return;
  }

  if (!newSoundEffect.value.description) {
    ElMessage.warning('请输入音效描述');
    return;
  }

  if (!props.shot.id) {
    ElMessage.warning('分镜ID不存在，无法创建音效');
    return;
  }

  try {
    // 设置生成状态为true
    isGeneratingSoundEffect.value = true;
    ElMessage.info('正在生成音效...');

    // 构建API参数 - 这里使用音频API的参数结构，实际项目中可能需要专门的音效API
    const params = {
      shotId: props.shot.id,
      text: newSoundEffect.value.description,
      voiceId: 'sound_effect', // 音效专用的voiceId
      audioType: 2, // 假设2代表音效类型
      speed: 1.0,
      vol: newSoundEffect.value.volume,
      pitch: 0,
      duration: newSoundEffect.value.duration,
      effectType: newSoundEffect.value.type
    };

    // 调用API创建音效 - 这里复用音频API，实际项目中可能需要专门的音效API
    const response = await createCanvasShotAudio(params);

    if (response.success) {
      ElMessage.success('音效生成成功');

      // 清空表单
      newSoundEffect.value = {
        description: '',
        duration: 3.0,
        volume: 1.0,
        type: 'ambient'
      };

      // 通知父组件更新画布详情
      emit('refresh-canvas');

      // 设置生成状态为false，然后关闭弹窗
      isGeneratingSoundEffect.value = false;

      // 关闭弹窗
      emit('update:modelValue', false);

      return true;
    } else {
      ElMessage.error(`音效生成失败: ${response.errMessage}`);
    }
  } catch (error) {
    console.error('生成音效异常:', error);
    ElMessage.error('生成音效出错，请稍后再试');
  } finally {
    // 无论成功还是失败，最终都将生成状态设置为false
    isGeneratingSoundEffect.value = false;
  }
  return false;
};

// 监听ESC键关闭弹框
const handleKeyDown = (e) => {
  if (e.key === 'Escape' && props.modelValue && !isGeneratingAudio.value && !isGeneratingSoundEffect.value) {
    handleClose();
  }
};

// 组件挂载时添加键盘事件监听
onMounted(() => {
  document.addEventListener('keydown', handleKeyDown);
});

// 组件卸载前移除键盘事件监听
onBeforeUnmount(() => {
  document.removeEventListener('keydown', handleKeyDown);
});

// 监听voiceToCopy变化，自动填充表单
watch(() => props.voiceToCopy, (newVoiceToCopy) => {
  if (newVoiceToCopy && props.modelValue) {
    // 自动填充文本
    newVoice.value.text = newVoiceToCopy.text || '';

    // 自动选择音色
    if (newVoiceToCopy.voiceId) {
      selectedVoiceId.value = newVoiceToCopy.voiceId;
    }
  }
}, { immediate: true });

// 监听弹窗打开状态，当弹窗打开时如果有voiceToCopy数据则自动填充
watch(() => props.modelValue, (isOpen) => {
  if (isOpen && props.voiceToCopy) {
    // 自动填充文本
    newVoice.value.text = props.voiceToCopy.text || '';

    // 自动选择音色
    if (props.voiceToCopy.voiceId) {
      selectedVoiceId.value = props.voiceToCopy.voiceId;
    }
  }
});
</script>

<style scoped>
/* 弹框基础样式 */
.audio-generation-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 11;
  animation: fadeIn 0.3s ease;
}

.dialog-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.dialog-container {
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1), 0 4px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  animation: scaleIn 0.3s ease;
  position: relative;
}

body.dark .dialog-container {
  background-color: var(--bg-primary);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 弹框头部 */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-secondary);
  border-bottom: 1px solid #00000030;
  color: #000000;
}

body.dark .dialog-header {
  background: var(--bg-card);
  color: var(--text-primary);
}

.dialog-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  color: rgb(45, 45, 45);
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(45, 45, 45, 0.131);
}

body.dark .close-button {
  color: var(--text-primary);
}
body.dark .close-button:hover {
  background-color: rgba(252, 252, 252, 0.117);
}

/* 弹框内容 */
.dialog-content {
  padding: 30px;
  max-height: calc(90vh - 80px);
  overflow-y: auto;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 表单样式 */
.generation-content-audio {
  /* height: 380px; */
  /* overflow-y: auto; */
  /* box-sizing: border-box; */
  /* overflow-x: hidden; */
}

.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  /* margin-bottom: 15px; */
}

.image-generation-textarea {
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
  padding-bottom: 14px;
}

body.dark .image-generation-textarea {
  background-color: rgba(204, 221, 255, .06);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
}

.form-item {
  margin-bottom: 0px;
  position: relative;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
  text-align: left;
}

body.dark .form-label {
  color: var(--text-primary);
}

.panel-action-audio {
  padding: 0;
  display: flex;
  justify-content: center;
}

/* 生成按钮样式 */
.generation-button {
  width: 100%;
  justify-content: center;
  padding: 6px 12px;
  font-size: 15px;
}

.points-cost {
  margin-left: 5px;
  font-size: 14px;
  opacity: 0.9;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  transition: color 0.3s;
  background-color: #706cefd7;
  padding: 8px 12px;
  border-radius: 18px;
}

.back-button:hover {
  background-color: #5855e9d7;
}

.back-button.is-generating {
  background-color: #5855e9d7;
  cursor: not-allowed;
  position: relative;
}

.loading-icon {
  animation: rotating 2s linear infinite;
}

.circular-icon {
  height: 16px;
  width: 16px;
  animation: rotating 2s linear infinite;
}

.path {
  stroke: white;
  stroke-width: 3;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

body.dark .back-button {
  background-color: rgba(112, 108, 239, 0.7);
}

body.dark .back-button:hover {
  background-color: rgba(88, 85, 233, 0.7);
}

/* Character count */
.character-count {
  position: absolute;
  right: 4px;
  bottom: 4px;
  text-align: right;
  font-size: 12px;
  color: #909399;
}

body.dark .character-count {
  color: var(--text-secondary);
}

/* 文本输入容器和高亮样式 */
.text-input-container {
  position: relative;
}

.text-input-field {
  position: relative;
  z-index: 1;
}

.text-highlight-overlay {
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  pointer-events: none;
  z-index: 2;
  border-radius: 4px;
  overflow: hidden;
}

.highlight-content {
  padding: 8px 11px;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
  color: transparent;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  border: 1px solid transparent;
  border-radius: 4px;
  min-height: 32px;
  box-sizing: border-box;
  background-color: transparent;
}

.pronunciation-highlight {
  background-color: #e6f7ff !important;
  border: 1px solid #91d5ff !important;
  border-radius: 3px !important;
  padding: 1px 3px !important;
  color: #1890ff !important;
  font-weight: 500 !important;
  position: relative;
  cursor: help;
  display: inline;
}

body.dark .pronunciation-highlight {
  background-color: rgba(24, 144, 255, 0.15) !important;
  border-color: rgba(145, 213, 255, 0.3) !important;
  color: #69b1ff !important;
}

/* 确保textarea透明背景以显示高亮 */
.text-input-field :deep(.el-textarea__inner) {
  background-color: transparent !important;
  position: relative;
  z-index: 1;
  color: #303133;
}

body.dark .text-input-field :deep(.el-textarea__inner) {
  color: var(--text-primary);
}

/* 音色选择器样式 */
.voice-selector-item {
  /* margin-top: 15px; */
}

.voice-selector-dropdown {
  width: 100%;
}

.voice-selector-trigger {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

body.dark .voice-selector-trigger {
  background-color: rgba(204, 221, 255, .06);
}

.voice-selector-trigger:hover {
  background-color: #ecf5ff;
}

body.dark .voice-selector-trigger:hover {
  background-color: rgba(204, 221, 255, .1);
}

.selected-voice {
  flex: 1;
  font-size: 14px;
  color: #606266;
  text-align: left;
}

body.dark .selected-voice {
  color: var(--text-secondary);
}

.voice-selector-icon {
  font-size: 14px;
  color: #909399;
  transition: transform 0.3s;
}

body.dark .voice-selector-icon {
  color: var(--text-tertiary);
}

.voice-selector-container {
  margin: 12px;
  min-height: 200px;
  max-height: 800px;
}

.voice-param-item {
  /* margin-top: 10px; */
}

.voice-param-item .el-slider {
  padding: 0 4px;
  box-sizing: border-box;
}

.voice-param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.voice-param-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.voice-param-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

body.dark .voice-param-label {
  color: var(--text-primary);
}

body.dark .voice-param-value {
  color: var(--text-primary);
}

/* 自定义滑块颜色 */
.voice-param-item :deep(.el-slider__bar) {
  background-color: #706cef8d;
}

.voice-param-item :deep(.el-slider__button) {
  border-color: #706cef9a;
}

body.dark .voice-param-item :deep(.el-slider__bar) {
  background-color: rgba(112, 108, 239, 0.542);
}

body.dark .voice-param-item :deep(.el-slider__button) {
  border-color: rgba(112, 108, 239, 0.46);
}

/* 情感选择器样式 */
.emotion-selector {
  width: 100%;
}

.emotion-selector :deep(.el-input__wrapper) {
  background-color: #f5f7fa;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05) inset !important;
}

body.dark .emotion-selector :deep(.el-input__wrapper) {
  background-color: rgba(204, 221, 255, .06);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05) inset !important;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-textarea__inner) {
  font-size: 13px;
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-slider__runway) {
  margin: 8px 0;
}

:deep(.el-slider__input) {
  width: 60px;
  margin-left: 10px;
}

/* Tab 样式 - 从 VideoGenerationPanel.vue 复制并调整 */
.generation-tabs {
  /* padding: 10px 10px 0 10px; */
  padding: 6px 0;
  /* border-bottom: 1px solid #8585852a; */
}

.asset-selector-tabs :deep(.el-tabs__header) {
  position: relative !important;
  margin: 0 !important;
}

.asset-selector-tabs :deep(.el-tabs__item) {
  color: #606266 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  height: 38px !important;
  line-height: 38px !important;
  transition: all 0.3s !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item) {
  color: var(--text-secondary) !important;
}

.asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #6366f1 !important;
}

.asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #4f46e5 0, #4f46e5 100%,
      transparent 0, transparent) !important;
  height: 2px !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #6366f1 0, #6366f1 100%,
      transparent 0, transparent) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  position: static !important;
  height: 1px !important;
  background-color: #e4e7ed !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--border-color) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav) {
  height: auto !important;
}

/* 多音字设置按钮样式 */
.pronunciation-setting-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #f0f2f5;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  color: #606266;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.pronunciation-setting-button:hover {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

body.dark .pronunciation-setting-button {
  background-color: rgba(204, 221, 255, .08);
  color: var(--text-secondary);
}

body.dark .pronunciation-setting-button:hover {
  background-color: rgba(204, 221, 255, .15);
  border-color: rgba(145, 213, 255, 0.3);
  color: #69b1ff;
}

.pronunciation-count {
  background-color: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

/* 多音字设置弹框样式 */
.pronunciation-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 12;
  animation: fadeIn 0.3s ease;
}

.pronunciation-dialog-container {
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
}

.pronunciation-description {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 13px;
  line-height: 1.6;
  color: #666;
  border-left: 3px solid #1890ff;
  text-align: left;
}

body.dark .pronunciation-description {
  background-color: rgba(204, 221, 255, .06);
  color: var(--text-secondary);
  border-left-color: #69b1ff;
}

.pronunciation-description p {
  margin: 0 0 6px 0;
}

.pronunciation-description p:last-child {
  margin-bottom: 0;
}

.pronunciation-description strong {
  color: #303133;
  font-weight: 600;
}

body.dark .pronunciation-description strong {
  color: var(--text-primary);
}

.pronunciation-description code {
  background-color: #f1f3f4;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #d73a49;
}

body.dark .pronunciation-description code {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ff7875;
}

.pronunciation-add-section {
  margin-bottom: 20px;
}

.pronunciation-input-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pronunciation-word-input {
  flex: 1;
}

.pronunciation-separator {
  font-size: 16px;
  font-weight: 500;
  color: #666;
  margin: 0 4px;
}

body.dark .pronunciation-separator {
  color: var(--text-secondary);
}

.pronunciation-pronunciation-input {
  flex: 2;
}

.pronunciation-list {
  /* margin-bottom: 20px; */
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.pronunciation-list-header {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

body.dark .pronunciation-list-header {
  color: var(--text-primary);
  border-bottom-color: var(--border-color);
}

.pronunciation-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  /* margin-bottom: 8px; */
}

body.dark .pronunciation-item {
  background-color: rgba(204, 221, 255, .04);
}

.pronunciation-entry-text {
  font-size: 14px;
  color: #303133;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

body.dark .pronunciation-entry-text {
  color: var(--text-primary);
}

.pronunciation-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

body.dark .pronunciation-dialog-actions {
  border-top-color: var(--border-color);
}

/* 辅助类 */
.ml-5 {
  margin-left: 5px;
}
</style>
